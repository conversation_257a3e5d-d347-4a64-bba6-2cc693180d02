import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<RootPage> createState() => RootPageState();
}

class RootPageState extends State<RootPage> with TickerProviderStateMixin {
  late final FHeader _header;

  final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

  @override
  void initState() {
    super.initState();
    final controller = FPopoverController(vsync: this);
    _header = FHeader(
      title: const Text('媒体库'),
      suffixes: [
        FHeaderAction(
          icon: const Icon(FIcons.settings),
          onPress: () => context.push(settingPath),
        ),
        FPopoverMenu.automatic(
          popoverController: controller,
          menuAnchor: Alignment.topRight,
          childAnchor: Alignment.bottomRight,
          menu: [
            FTileGroup(
              children: [
                FTile(
                  prefixIcon: const Icon(FIcons.library),
                  title: const Text('添加媒体库'),
                  onPress: () {
                    controller.toggle();
                    context.push(editMediaLibraryPath);
                  },
                ),
              ],
            ),
          ],
          child: FHeaderAction(
            icon: const Icon(FIcons.ellipsis),
            onPress: controller.toggle,
          ),
        ),
      ],
    );
  }

  void _showDeleteDialog(MediaLibrary library) {
    showAdaptiveDialog(
      context: context,
      builder:
          (context) => FDialog(
            direction: Axis.vertical,
            actions: [
              FButton(
                child: const Text('编辑'),
                onPress: () {
                  Navigator.pop(context);
                  context.push('$editMediaLibraryPath?id=${library.id}');
                },
              ),
              FButton(
                style: context.theme.buttonStyles.destructive,
                onPress: () {
                  Navigator.pop(context);
                  showAdaptiveDialog(
                    context: context,
                    builder:
                        (context) => FDialog(
                          direction: Axis.vertical,
                          title: const Text('删除媒体库'),
                          body: Text('确定要删除媒体库 "${library.name}" 吗？'),
                          actions: [
                            FButton(
                              onPress: () => Navigator.pop(context),
                              child: const Text('取消'),
                            ),
                            FButton(
                              style: context.theme.buttonStyles.destructive,
                              onPress: () {
                                Navigator.pop(context);
                                mediaLibraryService.deleteMediaLibrary(
                                  library.id,
                                );
                              },
                              child: const Text('删除'),
                            ),
                          ],
                        ),
                  );
                },
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      scaffoldStyle: context.theme.scaffoldStyle.copyWith(
        childPadding: EdgeInsets.zero,
      ),
      header: _header,
      child: Watch((_) {
        final libraries = mediaLibraryService.mediaLibraries.value;

        return FTileGroup(
          divider: FTileDivider.indented,
          style: tileGroupStyle(
            colors: context.theme.colors,
            typography: context.theme.typography.copyWith(
              base: context.theme.typography.xl,
            ),
            style: context.theme.style,
            newColors: context.theme.colors.copyWith(
              border: const Color.fromARGB(0, 238, 238, 238),
            ),
          ),
          children:
              libraries
                  .map(
                    (library) => FTile(
                      prefixIcon: const Icon(FIcons.folder),
                      title: Text(library.name),
                      subtitle: Text(library.url),
                      onPress: () {
                        context.push('$fileExplorerPath?id=${library.id}');
                      },
                      onLongPress: () async {
                        _showDeleteDialog(library);
                      },
                    ),
                  ) // 重复20个
                  .toList(),
        );
      }),
    );
  }
}
