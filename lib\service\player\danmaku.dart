import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:canvas_danmaku/danmaku_controller.dart';
import 'package:canvas_danmaku/models/danmaku_content_item.dart';
import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:dandanplay_flutter/model/danmaku_match.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/player/danmaku_optimizer.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:dandanplay_flutter/utils/danmaku_api_utils.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';

class DanmakuService {
  late DanmakuController controller;

  ConfigureService configureService = GetIt.I.get<ConfigureService>();
  GlobalPlayerService globalPlayerService = GetIt.I.get<GlobalPlayerService>();

  // 弹幕匹配状态信号
  final Signal<DanmakuMatchStatus> _danmakuMatchStatus = Signal(
    DanmakuMatchStatus.idle,
  );
  final Signal<DanmakuMatchResult?> _danmakuMatchResult = Signal(null);
  // 弹幕相关信号
  final Signal<List<Danmaku>> _danmakus = Signal([]);
  final Signal<DanmakuSettings> _danmakuSettings = Signal(DanmakuSettings());
  final Signal<bool> danmakuEnabled = Signal(true);
  late String currentVideoPath = globalPlayerService.currentVideoPath;
  late History history;
  late Duration duration;

  // 当前弹幕索引，用于跟踪已显示的弹幕位置
  int _currentDanmakuIndex = 0;

  /// 弹幕设置
  ReadonlySignal<DanmakuSettings> get danmakuSettings =>
      _danmakuSettings.readonly();

  /// 弹幕匹配状态
  ReadonlySignal<DanmakuMatchStatus> get danmakuMatchStatus =>
      _danmakuMatchStatus.readonly();

  /// 弹幕匹配结果
  ReadonlySignal<DanmakuMatchResult?> get danmakuMatchResult =>
      _danmakuMatchResult.readonly();

  Future<void> init() async {
    final sittings = await configureService.getDanmakuSettings();
    _danmakuSettings.value = sittings;
    controller.updateOption(sittings.toDanmakuOption());
  }

  void syncWithVideo(bool isPlaying) {
    if (isPlaying) {
      controller.resume();
    } else {
      controller.pause();
    }
  }

  void clear() {
    controller.clear();
    _currentDanmakuIndex = 0;
  }

  /// 重置弹幕位置，用于seek操作后重新开始弹幕显示
  void resetDanmakuPosition() {
    _currentDanmakuIndex = 0;
  }

  /// 根据当前播放位置更新弹幕显示
  void updatePlayPosition(Duration position) {
    final danmakus = _danmakus.value;
    if (danmakus.isEmpty) return;

    // 从当前索引开始检查需要显示的弹幕
    while (_currentDanmakuIndex < danmakus.length) {
      final danmaku = danmakus[_currentDanmakuIndex];

      // 如果弹幕时间还没到，停止检查
      if (danmaku.time > position + const Duration(milliseconds: 500)) {
        break;
      }

      // 跳过显示过的弹幕
      if (danmaku.time < position - const Duration(milliseconds: 500)) {
        _currentDanmakuIndex++;
        continue;
      }

      // 添加弹幕到显示器
      _addDanmakuToController(danmaku);
      _currentDanmakuIndex++;
    }
  }

  /// 将弹幕添加到控制器中显示
  void _addDanmakuToController(Danmaku danmaku) {
    try {
      // 根据弹幕类型转换为canvas_danmaku的类型
      DanmakuItemType danmakuType;
      switch (danmaku.type) {
        case 1:
          danmakuType = DanmakuItemType.scroll; // 滚动弹幕
          break;
        case 4:
          danmakuType = DanmakuItemType.bottom; // 底部弹幕
          break;
        case 5:
          danmakuType = DanmakuItemType.top; // 顶部弹幕
          break;
        default:
          danmakuType = DanmakuItemType.scroll; // 默认滚动弹幕
      }

      // 调用controller的addDanmaku方法
      controller.addDanmaku(
        DanmakuContentItem(
          danmaku.text,
          type: danmakuType,
          color: danmaku.color,
        ),
      );
    } catch (e) {
      debugPrint('添加弹幕失败: $e');
    }
  }

  /// 加载弹幕
  Future<void> loadDanmaku({bool force = false}) async {
    try {
      // 1. 检查本地缓存 (非强制刷新时)
      if (!force) {
        final cachedDanmakus = await _getCachedDanmakus(currentVideoPath);
        if (cachedDanmakus.isNotEmpty) {
          // 按时间排序弹幕
          cachedDanmakus.sort((a, b) => a.time.compareTo(b.time));
          _danmakus.value = cachedDanmakus;
          _currentDanmakuIndex = 0; // 重置弹幕索引
          debugPrint('从缓存加载弹幕: ${cachedDanmakus.length}条');
          // 更新匹配状态
          _danmakuMatchStatus.value = DanmakuMatchStatus.success;
          _danmakuMatchResult.value = DanmakuMatchResult.success(
            episodeName: '缓存弹幕',
            confidence: 1.0,
            danmakuCount: cachedDanmakus.length,
            matchDuration: Duration.zero,
            isNetworkVideo: currentVideoPath.startsWith('http'),
          );
          return;
        }
      }

      // 2. 如果没有缓存或强制刷新，尝试从弹弹play API获取（带重试机制）
      final danmakus = await _fetchDanmakusWithRetry(currentVideoPath);

      // 3. 保存到本地缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakus(currentVideoPath, danmakus);
      }

      // 4. 按时间排序并更新弹幕列表
      danmakus.sort((a, b) => a.time.compareTo(b.time));
      _danmakus.value = danmakus;
      _currentDanmakuIndex = 0; // 重置弹幕索引
      debugPrint('从API加载弹幕: ${danmakus.length}条');
    } catch (e) {
      debugPrint('加载弹幕失败: $e');
      // 加载失败时设置空列表，避免界面异常
      _danmakus.value = [];
    }
  }

  /// 从缓存获取弹幕数据
  Future<List<Danmaku>> _getCachedDanmakus(String videoPath) async {
    try {
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      final danmakuFile = File('${cacheDir.path}/$uniqueKey.json');

      if (!await danmakuFile.exists()) {
        return [];
      }

      // 读取并解析弹幕文件
      final jsonString = await danmakuFile.readAsString();
      final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;

      // 检查过期时间
      final expireTime = cacheData['expireTime'] as int?;
      if (expireTime != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now > expireTime) {
          debugPrint('弹幕缓存已过期，删除缓存文件');
          await danmakuFile.delete();
          return [];
        }

        // 解析弹幕数据
        final danmakusJson = cacheData['danmakus'] as List;
        return danmakusJson
            .map(
              (item) =>
                  Danmaku.fromJson(item['p'] as String, item['m'] as String),
            )
            .toList();
      } else {
        // 旧版本缓存格式，兼容处理
        debugPrint('发现旧版本缓存格式，删除并重新加载');
        await danmakuFile.delete();
        return [];
      }
    } catch (e) {
      debugPrint('读取缓存弹幕失败: $e');
      return [];
    }
  }

  /// 带重试机制的弹幕获取
  Future<List<Danmaku>> _fetchDanmakusWithRetry(String videoPath) async {
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 2);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('尝试获取弹幕，第$attempt次');
        return await _fetchDanmakusFromApi(videoPath);
      } catch (e) {
        debugPrint('第$attempt次获取弹幕失败: $e');

        // 检查是否为网络错误
        if (_isNetworkError(e)) {
          debugPrint('检测到网络错误，将重试');
        } else {
          debugPrint('非网络错误，停止重试');
          break; // 非网络错误，不重试
        }

        if (attempt == maxRetries) {
          rethrow; // 最后一次尝试失败，抛出异常
        }

        // 等待后重试
        await Future.delayed(retryDelay);
      }
    }

    return []; // 重试失败或非网络错误
  }

  /// 检查是否为网络错误
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket') ||
        errorString.contains('dns') ||
        errorString.contains('unreachable');
  }

  /// 从弹弹play API获取弹幕数据
  Future<List<Danmaku>> _fetchDanmakusFromApi(String videoPath) async {
    final startTime = DateTime.now();
    final isNetworkVideo =
        videoPath.startsWith('http://') || videoPath.startsWith('https://');

    try {
      // 重置匹配状态
      _danmakuMatchStatus.value = DanmakuMatchStatus.idle;

      // 计算视频文件信息
      final videoInfo = await _getVideoInfo(videoPath);
      if (videoInfo == null) {
        final duration = DateTime.now().difference(startTime);
        _danmakuMatchResult.value = DanmakuMatchResult.failure(
          errorMessage: '无法获取视频信息',
          matchDuration: duration,
          isNetworkVideo: isNetworkVideo,
        );
        _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
        debugPrint('无法获取视频信息');
        return [];
      }

      // 开始匹配
      _danmakuMatchStatus.value = DanmakuMatchStatus.matching;

      // 调用弹弹play API匹配视频
      final episodes = await DanmakuApiUtils.matchVideo(
        fileName: videoInfo['fileName'],
        fileHash: videoInfo['fileHash'],
        fileSize: videoInfo['fileSize'],
        duration: videoInfo['duration'],
      );

      if (episodes.isEmpty) {
        final duration = DateTime.now().difference(startTime);
        _danmakuMatchResult.value = DanmakuMatchResult.failure(
          errorMessage: '未找到匹配的节目',
          matchDuration: duration,
          isNetworkVideo: isNetworkVideo,
          fileSize: videoInfo['fileSize'],
          fileHash: videoInfo['fileHash'],
        );
        _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
        debugPrint('未找到匹配的节目');
        return [];
      }

      // 获取第一个匹配结果的弹幕
      final episode = episodes.first;
      final comments = await DanmakuApiUtils.getComments(episode.episodeId);

      // 匹配成功
      final duration = DateTime.now().difference(startTime);
      _danmakuMatchResult.value = DanmakuMatchResult.success(
        episodeName: '${episode.animeTitle} - ${episode.episodeTitle}',
        confidence: 1.0 - episode.shift, // 使用shift作为置信度的基础
        danmakuCount: comments.length,
        matchDuration: duration,
        isNetworkVideo: isNetworkVideo,
        fileSize: videoInfo['fileSize'],
        fileHash: videoInfo['fileHash'],
      );
      _danmakuMatchStatus.value = DanmakuMatchStatus.success;

      // 转换为内部弹幕格式
      return comments.map((comment) => comment.toDanmaku()).toList();
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      _danmakuMatchResult.value = DanmakuMatchResult.failure(
        errorMessage: e.toString(),
        matchDuration: duration,
        isNetworkVideo: isNetworkVideo,
      );
      _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
      debugPrint('从API获取弹幕失败: $e');
      return [];
    }
  }

  /// 保存弹幕数据
  Future<void> _saveDanmakus(String videoPath, List<Danmaku> danmakus) async {
    try {
      final isNetworkVideo = videoPath.startsWith('http');

      // 生成缓存文件路径
      final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
      final documentsDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${documentsDir.path}/danmaku');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('${cacheDir.path}/$uniqueKey.json');

      // 添加缓存元数据
      final cacheData = {
        'videoPath': videoPath,
        'cacheTime': DateTime.now().millisecondsSinceEpoch,
        'expireTime':
            DateTime.now()
                .add(
                  DanmakuOptimizer.getCacheExpiration(
                    danmakuCount: danmakus.length,
                    isNetworkVideo: isNetworkVideo,
                  ),
                )
                .millisecondsSinceEpoch,
        'danmakus': danmakus.map((danmaku) => danmaku.toJson()).toList(),
      };

      await cacheFile.writeAsString(jsonEncode(cacheData));
      final historyService = GetIt.I.get<HistoryService>();
      await historyService.saveDanmakuUpdateTime(videoPath: videoPath);

      debugPrint('弹幕缓存保存成功: ${cacheFile.path}, 弹幕数量: ${danmakus.length}');
    } catch (e) {
      debugPrint('保存弹幕缓存失败: $e');
    }
  }

  /// 获取视频文件信息（带进度状态）
  Future<Map<String, dynamic>?> _getVideoInfo(String videoPath) async {
    try {
      if (videoPath.startsWith('http://') || videoPath.startsWith('https://')) {
        // 网络视频：尝试获取真实文件hash
        _danmakuMatchStatus.value = DanmakuMatchStatus.downloadingVideo;

        try {
          // 使用异步网络hash计算，避免阻塞主线程
          final hashResult =
              await DanmakuOptimizer.calculateNetworkVideoHashAsync(
                videoPath,
                onProgress: (downloaded, total) {
                  debugPrint('下载进度: $downloaded / ${total ?? "未知"}');
                },
              );

          _danmakuMatchStatus.value = DanmakuMatchStatus.calculatingHash;

          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': hashResult.hash,
            'fileSize': hashResult.totalFileSize ?? 0,
            'duration': duration.inSeconds,
          };
        } catch (e) {
          debugPrint('网络视频hash计算失败，回退到URL hash: $e');
          // 回退到URL hash
          return {
            'fileName': Uri.parse(videoPath).pathSegments.last,
            'fileHash': CryptoUtils.calculateNetworkVideoHash(videoPath),
            'fileSize': 0,
            'duration': duration.inSeconds,
          };
        }
      } else {
        // 本地文件：计算实际文件信息
        _danmakuMatchStatus.value = DanmakuMatchStatus.calculatingHash;

        final file = File(videoPath);
        if (!await file.exists()) {
          return null;
        }

        final fileSize = await file.length();
        final fileHash = await DanmakuOptimizer.calculateVideoHashAsync(
          videoPath,
        );
        final fileName = file.path.split('/').last;

        return {
          'fileName': fileName,
          'fileHash': fileHash,
          'fileSize': fileSize,
          'duration': duration.inSeconds,
        };
      }
    } catch (e) {
      debugPrint('获取视频信息失败: $e');
      return null;
    }
  }

  /// 检查并更新弹幕数据（自动刷新）
  Future<void> refreshDanmakus() async {
    try {
      // 检查缓存是否过期（例如：超过72小时）
      if (history.danmakuUpdateTime > 0) {
        final lastUpdate = DateTime.fromMillisecondsSinceEpoch(
          history.danmakuUpdateTime,
        );
        final now = DateTime.now();
        final difference = now.difference(lastUpdate);

        // 如果缓存时间小于72小时，不需要更新
        if (difference.inHours < 72) {
          debugPrint('弹幕缓存仍然有效，跳过自动更新');
          return;
        }
      }

      // 强制重新加载弹幕
      debugPrint('弹幕缓存已过期，自动重新加载');
      await loadDanmaku(force: true);
    } catch (e) {
      debugPrint('自动刷新弹幕失败: $e');
    }
  }

  /// 搜索番剧集数
  Future<List<Anime>> searchEpisodes(String animeName) async {
    try {
      _danmakuMatchStatus.value = DanmakuMatchStatus.searching;

      final animes = await DanmakuApiUtils.searchEpisodes(animeName);

      return animes;
    } catch (e) {
      _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
      debugPrint('搜索番剧失败: $e');
      rethrow;
    }
  }

  /// 选择episodeId并加载弹幕
  Future<void> selectEpisodeAndLoadDanmaku(String episodeId) async {
    try {
      _danmakuMatchStatus.value = DanmakuMatchStatus.matching;

      // 获取弹幕
      final comments = await DanmakuApiUtils.getComments(episodeId);

      // 转换为内部弹幕格式并排序
      final danmakus = comments.map((comment) => comment.toDanmaku()).toList();
      danmakus.sort((a, b) => a.time.compareTo(b.time));

      // 保存到缓存
      if (danmakus.isNotEmpty) {
        await _saveDanmakus(currentVideoPath, danmakus);
      }

      // 更新弹幕列表
      _danmakus.value = danmakus;
      _currentDanmakuIndex = 0;

      // 更新匹配状态
      _danmakuMatchStatus.value = DanmakuMatchStatus.success;
      _danmakuMatchResult.value = DanmakuMatchResult.success(
        episodeName: '手动选择的集数',
        confidence: 1.0,
        danmakuCount: danmakus.length,
        matchDuration: Duration.zero,
        isNetworkVideo: currentVideoPath.startsWith('http'),
      );

      debugPrint('手动选择弹幕加载成功: ${danmakus.length}条');
    } catch (e) {
      _danmakuMatchStatus.value = DanmakuMatchStatus.failed;
      _danmakuMatchResult.value = DanmakuMatchResult.failure(
        errorMessage: e.toString(),
        matchDuration: Duration.zero,
        isNetworkVideo: currentVideoPath.startsWith('http'),
      );
      debugPrint('手动选择弹幕加载失败: $e');
      rethrow;
    }
  }

  /// 更新弹幕设置
  void updateDanmakuSettings(DanmakuSettings settings) {
    _danmakuSettings.value = settings;
    configureService.setDanmakuSettings(settings);
    controller.updateOption(settings.toDanmakuOption());
    debugPrint('弹幕设置已更新: $settings');
  }

  void dispose() {
    _danmakus.dispose();
    _danmakuSettings.dispose();
    _danmakuMatchStatus.dispose();
    _danmakuMatchResult.dispose();
  }
}
