import 'package:dandanplay_flutter/utils/video_player_utils.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

/// 视频播放器演示页面
class VideoDemoPage extends StatefulWidget {
  const VideoDemoPage({super.key});

  @override
  State<VideoDemoPage> createState() => _VideoDemoPageState();
}

class _VideoDemoPageState extends State<VideoDemoPage> {
  final TextEditingController _urlController = TextEditingController();
  final TextEditingController _pathController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();

  // 一些示例视频URL
  final List<Map<String, String>> _sampleVideos = [
    {
      'title': 'Big Buck Bunny (MP4)',
      'url': 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    },
    {
      'title': 'Elephant Dream (MP4)',
      'url': 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    },
    {
      'title': 'For Bigger Blazes (MP4)',
      'url': 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    },
  ];

  @override
  void dispose() {
    _urlController.dispose();
    _pathController.dispose();
    _titleController.dispose();
    super.dispose();
  }

  void _playNetworkVideo() {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      _showError('请输入视频URL');
      return;
    }

    if (!VideoPlayerUtils.isNetworkVideo(url)) {
      _showError('请输入有效的网络视频URL（http://或https://）');
      return;
    }

    final title = _titleController.text.trim();
    VideoPlayerUtils.playNetworkVideo(
      context,
      url: url,
      title: title.isEmpty ? null : title,
    );
  }

  void _playLocalVideo() {
    final path = _pathController.text.trim();
    if (path.isEmpty) {
      _showError('请输入本地视频文件路径');
      return;
    }

    final title = _titleController.text.trim();
    VideoPlayerUtils.playLocalVideo(
      context,
      filePath: path,
      title: title.isEmpty ? null : title,
    );
  }

  void _playSampleVideo(String url, String title) {
    VideoPlayerUtils.playNetworkVideo(
      context,
      url: url,
      title: title,
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: const FHeader(
        title: Text('视频播放器演示'),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 网络视频播放部分
            _buildNetworkVideoSection(),
            const SizedBox(height: 24),
            
            // 本地视频播放部分
            _buildLocalVideoSection(),
            const SizedBox(height: 24),
            
            // 示例视频部分
            _buildSampleVideosSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkVideoSection() {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '播放网络视频',
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FTextField(
              controller: _urlController,
              label: const Text('视频URL'),
              hint: 'https://example.com/video.mp4',
            ),
            const SizedBox(height: 12),
            FTextField(
              controller: _titleController,
              label: const Text('标题（可选）'),
              hint: '自定义视频标题',
            ),
            const SizedBox(height: 16),
            FButton(
              onPress: _playNetworkVideo,
              child: const Text('播放网络视频'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalVideoSection() {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '播放本地视频',
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FTextField(
              controller: _pathController,
              label: const Text('本地文件路径'),
              hint: '/storage/emulated/0/Movies/video.mp4',
            ),
            const SizedBox(height: 16),
            FButton(
              onPress: _playLocalVideo,
              child: const Text('播放本地视频'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSampleVideosSection() {
    return FCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '示例视频',
              style: context.theme.typography.lg.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._sampleVideos.map((video) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: FButton(
                style: FButtonStyle.outline,
                onPress: () => _playSampleVideo(video['url']!, video['title']!),
                child: Text(video['title']!),
              ),
            )),
          ],
        ),
      ),
    );
  }
}
