# 配置服务 (ConfigureService) 使用文档

## 概述

ConfigureService 是一个基于 SharedPreferences 的类型安全配置管理服务，提供了完整的配置项管理功能，包括：

- ✅ **类型安全**：使用枚举作为key，避免字符串硬编码
- ✅ **默认值支持**：每个配置项都有明确的默认值
- ✅ **接口化访问**：提供统一的get/set接口和类型安全的专用方法
- ✅ **数据验证**：自动验证配置值的有效范围
- ✅ **批量操作**：支持批量获取和设置配置
- ✅ **导入导出**：支持JSON格式的配置导入导出
- ✅ **持久化存储**：基于SharedPreferences的可靠存储

## 快速开始

### 1. 获取服务实例

```dart
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:get_it/get_it.dart';

// 获取配置服务实例（需要先在ServiceLocator中注册）
final configService = GetIt.I.get<ConfigureService>();
```

### 2. 基本用法

```dart
// 使用通用方法
bool danmakuEnabled = await configService.get<bool>(ConfigKey.danmakuEnabled);
await configService.set<bool>(ConfigKey.danmakuEnabled, false);

// 使用类型安全的专用方法（推荐）
bool enabled = await configService.getDanmakuEnabled();
await configService.setDanmakuEnabled(false);

double speed = await configService.getPlaybackSpeed();
await configService.setPlaybackSpeed(1.5);
```

## 可用配置项

### 弹幕相关配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `danmakuEnabled` | bool | true | 是否启用弹幕 |
| `danmakuOpacity` | double | 0.8 | 弹幕透明度 (0.0-1.0) |
| `danmakuFontSizeScale` | double | 1.0 | 字体大小倍数 (0.5-2.0) |
| `danmakuSpeedScale` | double | 1.0 | 弹幕速度倍数 (0.5-2.0) |
| `danmakuShowTop` | bool | true | 是否显示顶部弹幕 |
| `danmakuShowBottom` | bool | true | 是否显示底部弹幕 |
| `danmakuShowScroll` | bool | true | 是否显示滚动弹幕 |
| `danmakuDensity` | double | 1.0 | 弹幕密度 (0.1-1.0) |

### 播放器相关配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `playbackSpeed` | double | 1.0 | 播放速度 (0.25-3.0) |
| `autoPlay` | bool | false | 是否自动播放 |
| `autoFullscreen` | bool | false | 是否自动全屏 |
| `rememberPosition` | bool | true | 是否记住播放位置 |
| `skipIntroEnabled` | bool | false | 是否启用跳过片头 |

### 界面相关配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `themeMode` | int | 0 | 主题模式 (0:系统, 1:浅色, 2:深色) |
| `language` | String | 'zh_CN' | 语言设置 |
| `showThumbnails` | bool | true | 是否显示缩略图 |

### 网络相关配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `networkTimeout` | int | 30000 | 网络超时时间 (毫秒, 5000-300000) |
| `maxRetryCount` | int | 3 | 最大重试次数 (0-10) |
| `bufferSize` | int | 1048576 | 缓冲区大小 (字节, 64KB-16MB) |

### 其他配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `firstLaunch` | bool | true | 是否首次启动 |
| `lastVersion` | String | '1.0.0' | 上次运行的版本号 |

## API 参考

### 通用方法

```dart
// 获取配置值
Future<T> get<T>(ConfigKey key);

// 设置配置值
Future<void> set<T>(ConfigKey key, T value);

// 重置配置到默认值
Future<void> resetToDefault(ConfigKey key);

// 重置所有配置到默认值
Future<void> resetAllToDefault();
```

### 批量操作

```dart
// 获取所有配置
Future<Map<ConfigKey, dynamic>> getAll();

// 批量设置配置
Future<void> setAll(Map<ConfigKey, dynamic> configs);
```

### 便捷方法

```dart
// 检查配置项是否存在（非默认值）
Future<bool> hasValue(ConfigKey key);

// 获取所有已设置的配置项键
Future<Set<ConfigKey>> getSetKeys();
```

### 导入导出

```dart
// 导出配置为JSON字符串
Future<String> exportToJson();

// 从JSON字符串导入配置
Future<void> importFromJson(String jsonString);
```

## 使用示例

### 弹幕配置示例

```dart
// 获取弹幕配置
bool enabled = await configService.getDanmakuEnabled();
double opacity = await configService.getDanmakuOpacity();
double fontSize = await configService.getDanmakuFontSizeScale();

// 设置弹幕配置
await configService.setDanmakuEnabled(true);
await configService.setDanmakuOpacity(0.9);
await configService.setDanmakuFontSizeScale(1.2);

print('弹幕配置: 启用=$enabled, 透明度=$opacity, 字体大小=${fontSize}x');
```

### 播放器配置示例

```dart
// 设置播放器配置
await configService.setPlaybackSpeed(1.25);
await configService.setAutoPlay(true);
await configService.setRememberPosition(true);

// 获取播放器配置
double speed = await configService.getPlaybackSpeed();
bool autoPlay = await configService.getAutoPlay();
bool rememberPos = await configService.getRememberPosition();

print('播放器配置: 速度=${speed}x, 自动播放=$autoPlay, 记住位置=$rememberPos');
```

### 批量操作示例

```dart
// 批量设置配置
Map<ConfigKey, dynamic> configs = {
  ConfigKey.danmakuEnabled: true,
  ConfigKey.danmakuOpacity: 0.8,
  ConfigKey.playbackSpeed: 1.0,
  ConfigKey.autoPlay: false,
  ConfigKey.themeMode: 1, // 浅色主题
};

await configService.setAll(configs);

// 获取所有配置
Map<ConfigKey, dynamic> allConfigs = await configService.getAll();
print('当前配置项数量: ${allConfigs.length}');
```

### 导入导出示例

```dart
// 导出配置
String configJson = await configService.exportToJson();
print('配置已导出: $configJson');

// 导入配置
String importConfig = '''
{
  "danmakuEnabled": false,
  "playbackSpeed": 1.5,
  "themeMode": 2
}
''';

await configService.importFromJson(importConfig);
print('配置导入完成');
```

## 添加新配置项

### 步骤1：在ConfigKey枚举中添加新键

```dart
enum ConfigKey {
  // ... 现有配置项
  videoQuality, // 新增配置项
}
```

### 步骤2：在ConfigRegistry中添加配置项定义

```dart
ConfigKey.videoQuality: ConfigItem<String>(
  key: ConfigKey.videoQuality,
  defaultValue: 'auto',
  storageKey: 'video_quality',
  description: '视频质量设置',
),
```

### 步骤3：在ConfigureService中添加专用方法（可选）

```dart
/// 获取视频质量
Future<String> getVideoQuality() async {
  return await get<String>(ConfigKey.videoQuality);
}

/// 设置视频质量
Future<void> setVideoQuality(String quality) async {
  await set<String>(ConfigKey.videoQuality, quality);
}
```

## 注意事项

1. **范围验证**：某些配置项会自动验证值的有效范围，超出范围的值会被自动调整
2. **类型安全**：建议使用类型安全的专用方法而不是通用的get/set方法
3. **默认值**：如果配置项不存在或读取失败，会自动返回默认值
4. **错误处理**：所有方法都包含适当的错误处理和日志记录
5. **性能**：配置服务已经过优化，但建议避免频繁的读写操作

## 测试

项目包含完整的单元测试，覆盖所有功能：

```bash
flutter test test/configure_service_test.dart
```

测试包括：
- 基本功能测试
- 类型安全方法测试
- 批量操作测试
- 便捷方法测试
- 导入导出测试
- 错误处理测试
