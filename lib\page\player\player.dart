import 'dart:async';
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:canvas_danmaku/canvas_danmaku.dart';
import 'package:dandanplay_flutter/page/player/right_drawer.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/utils/bit_converter.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:video_player/video_player.dart';
import '../../service/player/player_state.dart';
import '../../service/player/player.dart';
import '../../service/player/ui_state.dart';
import 'gesture.dart';
import 'indicator.dart';

class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({super.key});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerService _playerService;
  late PlayerUIState _uiState;
  final _fileExplorerService = GetIt.I.get<FileExplorerService>();
  final _globalPlayerService = GetIt.I.get<GlobalPlayerService>();

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializePlayer();
  }

  /// 初始化服务
  void _initializeServices() {
    // 初始化UI状态管理器
    _uiState = PlayerUIState();
    _uiState.init();
    _playerService = VideoPlayerService();
  }

  /// 初始化播放器
  Future<void> _initializePlayer() async {
    try {
      await _playerService.initialize();
      // 显示控制栏
      _uiState.showControlsTemporarily();
    } catch (e) {
      debugPrint('初始化播放器失败: $e');
    }
  }

  @override
  void dispose() {
    // 释放UI状态管理器
    _uiState.dispose();
    _playerService.dispose();
    // 恢复系统UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    // 释放音量和亮度控制服务
    BrightnessVolumeService.dispose();
    super.dispose();
  }

  /// 切换播放/暂停
  void _togglePlayPause() {
    _playerService.togglePlayPause();
    _uiState.showControlsTemporarily();
  }

  @override
  Widget build(BuildContext context) {
    final playerState = _playerService.playerState.watch(context);
    final errorMessage = _playerService.errorMessage.watch(context);
    if (playerState.hasError) {
      return _buildErrorWidget(errorMessage);
    }
    if (playerState.isLoading || playerState == PlayerState.idle) {
      return _buildLoadingWidget();
    }
    return _buildVideoPlayer();
  }

  Widget _buildErrorWidget(String? errorMessage) {
    return FScaffold(
      header: FHeader(
        suffixes: [FHeaderAction.back(onPress: Navigator.of(context).pop)],
      ),
      scaffoldStyle: FScaffoldStyle.inherit(
        colors: context.theme.colors.copyWith(background: Colors.black),
        style: context.theme.style,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(FIcons.circleX, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            const Text(
              '视频加载失败',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage ?? '未知错误',
              style: const TextStyle(color: Colors.white70, fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 28),
            FButton(
              onPress: () {
                _initializePlayer();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          const Text(
            '正在加载视频...',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return FScaffold(
      resizeToAvoidBottomInset: false,
      scaffoldStyle: FScaffoldStyle.inherit(
        colors: context.theme.colors.copyWith(background: Colors.black),
        style: context.theme.style.copyWith(pagePadding: EdgeInsets.zero),
      ),
      child: VideoPlayerGestureDetector(
        onSingleTap: () {
          if (_uiState.showControls.value) {
            _uiState.updateControlsVisibility(false);
          } else {
            _uiState.showControlsTemporarily();
          }
        },
        onDoubleTap: _togglePlayPause,
        onLongPressStart: () {
          _uiState.startLongPress(_playerService.playbackSpeed.value * 2);
          HapticFeedback.vibrate();
          _playerService.doubleSpeed(true);
        },
        onLongPressEnd: () {
          _uiState.endLongPress();
          _playerService.doubleSpeed(false);
        },
        onPanStart: () {
          _uiState.startGesture(
            initialVolume: _uiState.currentVolume.value,
            initialBrightness: _uiState.currentBrightness.value,
            initialPosition: _playerService.position.value,
          );
        },
        onPanEnd: () {
          _uiState.endGesture();
        },
        onVerticalDragLeft: (offset) {
          _adjustBrightness(offset);
        },
        onVerticalDragRight: (offset) {
          _adjustVolume(offset);
        },
        onHorizontalDrag: (offset) {
          _adjustProgress(offset, false);
        },
        onHorizontalDragEnd: (offset) {
          _adjustProgress(offset, true);
        },
        child: Stack(
          children: [
            _buildVideoPlayerWidget(),
            // 弹幕层
            _buildDanmakuLayer(),
            // 控制栏
            ..._buildAnimatedControls(),
            // 状态指示器
            _buildStatusIndicatorOverlay(),
            // 进度指示器
            Watch((context) {
              final showProgressIndicator = _uiState.showProgressIndicator
                  .watch(context);
              return showProgressIndicator
                  ? _buildProgressIndicatorOverlay()
                  : Container();
            }),
            // 缓冲指示器
            _buildBufferingIndicator(),
            // 通知组件
            _buildNotificationOverlay(),
          ],
        ),
      ),
    );
  }

  /// 构建带动画的控制栏
  List<Widget> _buildAnimatedControls() {
    return [
      // 顶部控制栏
      Watch((context) {
        final showControls = _uiState.showControls.watch(context);
        final isGesturing = _uiState.isGesturing.watch(context);
        final shouldShow = showControls && !isGesturing;

        return AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          top: shouldShow ? 0 : -100,
          left: 0,
          right: 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: shouldShow ? 1.0 : 0.0,
            child: _buildTopControls(),
          ),
        );
      }),
      // 底部控制栏
      Watch((context) {
        final showControls = _uiState.showControls.watch(context);
        final isGesturing = _uiState.isGesturing.watch(context);
        final shouldShow = showControls && !isGesturing;

        return AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          bottom: shouldShow ? 0 : -100,
          left: 0,
          right: 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: shouldShow ? 1.0 : 0.0,
            child: _buildBottomControls(),
          ),
        );
      }),
    ];
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black87, Colors.transparent],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  IconButton(
                    icon: const Icon(
                      FIcons.arrowLeft,
                      color: Colors.white,
                      size: 28,
                    ),
                    onPressed: () => context.pop(),
                  ),
                  Watch((context) {
                    final videoName = _playerService.videoName.watch(context);
                    return Text(
                      videoName,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    );
                  }),
                ],
              ),
              Row(
                children: [
                  // 弹幕开关
                  Watch((context) {
                    final enabled =
                        _playerService.danmakuService.danmakuEnabled.value;
                    return IconButton(
                      icon: Icon(
                        enabled ? FIcons.captions : FIcons.captionsOff,
                        color: Colors.white,
                        size: 28,
                      ),
                      onPressed: () {
                        _playerService.danmakuService.danmakuEnabled.value =
                            !enabled;
                      },
                    );
                  }),
                  // 弹幕设置
                  IconButton(
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                      size: 28,
                    ),
                    onPressed:
                        () => _showRightDrawer(RightDrawerType.danmakuActions),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
            colors: [Colors.black87, Colors.transparent],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 进度条
              _buildProgressBar(),
              // 播放控制按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 左侧按钮组
                  Row(
                    children: [
                      // 播放/暂停
                      Watch((context) {
                        final playerState = _playerService.playerState.watch(
                          context,
                        );
                        return Padding(
                          padding: EdgeInsetsGeometry.symmetric(horizontal: 2),
                          child: IconButton(
                            icon: Icon(
                              playerState.isPlaying
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              color: Colors.white,
                              size: 32,
                            ),
                            onPressed: _togglePlayPause,
                          ),
                        );
                      }),
                      // 上一个视频
                      Watch((context) {
                        final currentIndex = _globalPlayerService.currentIndex
                            .watch(context);
                        final canSwitch = computed(() {
                          final asyncList = _fileExplorerService.files.value;
                          if (asyncList.hasValue) {
                            final list = asyncList.requireValue;
                            final index = currentIndex - 1;
                            if (index >= 0 && index < list.length) {
                              return !list[index].isFolder;
                            }
                          }
                          return false;
                        });
                        return canSwitch.value
                            ? Padding(
                              padding: EdgeInsetsGeometry.symmetric(
                                horizontal: 2,
                              ),
                              child: IconButton(
                                icon: const Icon(
                                  Icons.skip_previous,
                                  color: Colors.white,
                                  size: 28,
                                ),
                                onPressed: () {
                                  final path = _fileExplorerService.switchVideo(
                                    currentIndex - 1,
                                  );
                                  if (path == null) {
                                    return;
                                  }
                                  _switchVideo(path, currentIndex - 1);
                                },
                              ),
                            )
                            : Container();
                      }),
                      // 下一个视频
                      Watch((context) {
                        final currentIndex = _globalPlayerService.currentIndex
                            .watch(context);
                        final canSwitch = computed(() {
                          final asyncList = _fileExplorerService.files.value;
                          if (asyncList.hasValue) {
                            final list = asyncList.requireValue;
                            final index = currentIndex + 1;
                            if (index >= 0 && index < list.length) {
                              return !list[index].isFolder;
                            }
                          }
                          return false;
                        });
                        return canSwitch.value
                            ? Padding(
                              padding: EdgeInsetsGeometry.symmetric(
                                horizontal: 2,
                              ),
                              child: IconButton(
                                icon: const Icon(
                                  Icons.skip_next,
                                  color: Colors.white,
                                  size: 28,
                                ),
                                onPressed: () {
                                  final path = _fileExplorerService.switchVideo(
                                    currentIndex + 1,
                                  );
                                  if (path == null) {
                                    return;
                                  }
                                  _switchVideo(path, currentIndex + 1);
                                },
                              ),
                            )
                            : Container();
                      }),
                    ],
                  ),
                  // 右侧按钮组
                  Row(
                    children: [
                      // 选集
                      IconButton(
                        icon: const Icon(
                          FIcons.listVideo,
                          color: Colors.white,
                          size: 28,
                        ),
                        onPressed:
                            () => _showRightDrawer(RightDrawerType.episode),
                      ),
                      const SizedBox(width: 4),
                      // 速度控制
                      Watch((context) {
                        final speed = _playerService.playbackSpeed.watch(
                          context,
                        );
                        return TextButton(
                          onPressed:
                              () => _showRightDrawer(RightDrawerType.speed),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                            textStyle: const TextStyle(fontSize: 16),
                          ),
                          child: Text('${speed.toStringAsFixed(2)}x'),
                        );
                      }),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建视频播放器组件
  Widget _buildVideoPlayerWidget() {
    final controller = _playerService.controller;
    if (controller == null || !controller.value.isInitialized) {
      return Container();
    }
    return Center(
      child: AspectRatio(
        aspectRatio: controller.value.aspectRatio,
        child: VideoPlayer(controller),
      ),
    );
  }

  /// 构建弹幕层
  Widget _buildDanmakuLayer() {
    return Watch((context) {
      if (!_playerService.danmakuService.danmakuEnabled.watch(context)) {
        return Container();
      }
      return DanmakuScreen(
        createdController: (controller) {
          _playerService.danmakuService.controller = controller;
        },
        option: DanmakuOption(),
      );
    });
  }

  /// 构建状态指示器覆盖层（音量、亮度、速度）
  Widget _buildStatusIndicatorOverlay() {
    return Watch((context) {
      final activeIndicator = _uiState.activeIndicator.watch(context);
      final indicatorValue = _uiState.indicatorValue.watch(context);
      if (activeIndicator == null) {
        return const SizedBox.shrink();
      }
      double displayValue;
      switch (activeIndicator) {
        case IndicatorType.volume:
          displayValue = _uiState.currentVolume.watch(context);
          break;
        case IndicatorType.brightness:
          displayValue = _uiState.currentBrightness.watch(context);
          break;
        case IndicatorType.speed:
          displayValue = indicatorValue;
          break;
      }
      return Positioned(
        top: 100,
        left: 0,
        right: 0,
        child: Center(
          child: StatusIndicator(
            type: activeIndicator,
            value: displayValue,
            isVisible: true,
          ),
        ),
      );
    });
  }

  /// 构建缓冲指示器
  Widget _buildBufferingIndicator() {
    return Watch((context) {
      final playerState = _playerService.playerState.watch(context);
      final bitRate = _playerService.bitRate.watch(context);
      if (playerState != PlayerState.buffering) {
        return const SizedBox.shrink();
      }
      return Positioned.fill(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 8),
              Text(
                '缓冲中...',
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
              Text(
                BitConverter.format(bitRate),
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建通知覆盖层
  Widget _buildNotificationOverlay() {
    return Positioned(
      left: 16,
      bottom: 80, // 在底部控制栏外的位置
      child: SizedBox(
        width: 300,
        height: MediaQuery.of(context).size.height - 80,
        child: FToaster(
          style: context.theme.toasterStyle.copyWith(
            expandBehavior: FToasterExpandBehavior.always,
          ),
          child: Builder(
            builder: (context) {
              _globalPlayerService.notificationContext = context;
              return Container();
            },
          ),
        ),
      ),
    );
  }

  /// 调整亮度
  void _adjustBrightness(double offset) {
    final initialBrightness = _uiState.initialBrightnessOnPan;
    if (initialBrightness == null) return;
    final newBrightness = (initialBrightness + offset).clamp(0.0, 1.0);
    _uiState.setBrightness(newBrightness);
    BrightnessVolumeService.setBrightness(newBrightness);
  }

  /// 调整音量
  void _adjustVolume(double offset) {
    final initialVolume = _uiState.initialVolumeOnPan;
    if (initialVolume == null) return;
    final newVolume = (initialVolume + offset).clamp(0.0, 1.0);
    _uiState.setVolume(newVolume);
    BrightnessVolumeService.setVolume(newVolume);
  }

  /// 调整播放进度
  void _adjustProgress(Duration offset, bool end) {
    final initialPosition = _uiState.initialPositionOnPan;
    if (initialPosition == null) return;
    final duration = _playerService.duration.value;
    if (duration.inMilliseconds <= 0) return;
    final newPosition = (initialPosition + offset);
    // 限制在视频时长范围内
    final clampedPosition = newPosition.inMilliseconds.clamp(
      0,
      duration.inMilliseconds,
    );
    final finalPosition = Duration(milliseconds: clampedPosition);
    final newPositionText = formatDuration(finalPosition);
    final durationText = formatDuration(duration);
    _uiState.setProgressIndicator('$newPositionText / $durationText');
    if (end) {
      // seekTo方法已经包含了弹幕清空和重置逻辑
      _playerService.seekTo(finalPosition);
    }
  }

  /// 构建进度指示器覆盖层
  Widget _buildProgressIndicatorOverlay() {
    return Watch((context) {
      final progressText = _uiState.progressIndicatorText.watch(context);
      return Center(
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            progressText,
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
      );
    });
  }

  Widget _buildProgressBar() {
    return Watch((context) {
      final progressState = _playerService.progressBarState.watch(context);
      return ProgressBar(
        progress: progressState.position,
        total: progressState.duration,
        buffered: progressState.bufferedPosition,
        thumbRadius: 8,
        thumbGlowRadius: 18,
        timeLabelTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 12.0,
          fontFeatures: [FontFeature.tabularFigures()],
        ),
        onSeek: _playerService.seekTo,
      );
    });
  }

  void _switchVideo(String selectedPath, int index) {
    final service = _playerService;
    _globalPlayerService.currentIndex.value = index;
    _globalPlayerService.currentVideoPath = selectedPath;
    setState(() {
      _playerService = VideoPlayerService();
    });
    _initializePlayer();
    service.dispose();
  }

  void _showRightDrawer(RightDrawerType drawerType) {
    // 隐藏主控制栏以避免重叠
    _uiState.updateControlsVisibility(false);
    showFSheet(
      context: context,
      side: FLayout.rtl,
      builder: (context) {
        return RightDrawerContent(
          drawerType: drawerType,
          playerService: _playerService,
          onEpisodeSelected: _switchVideo,
          onDrawerChanged: (newType) {
            // Pop current and show new one
            Navigator.pop(context);
            _showRightDrawer(newType);
          },
        );
      },
    );
  }
}
