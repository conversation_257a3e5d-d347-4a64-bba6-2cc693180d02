import 'dart:convert';
import 'dart:io';

import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';

class FileExplorerPage extends StatefulWidget {
  final int mediaLibraryId;
  const FileExplorerPage({super.key, required this.mediaLibraryId});

  @override
  State<FileExplorerPage> createState() => _FileExplorerPageState();
}

class _FileExplorerPageState extends State<FileExplorerPage> {
  MediaLibrary? _mediaLibrary;
  final FileExplorerService _fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  final GlobalPlayerService _globalPlayerService =
      GetIt.I.get<GlobalPlayerService>();

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    final mediaLibrary = await mediaLibraryService.getMediaLibrary(
      widget.mediaLibraryId,
    );
    if (mediaLibrary == null) {
      return;
    }
    late FileExplorerProvider provider;
    switch (mediaLibrary.mediaType) {
      case MediaType.webdav:
        provider = WebDAVFileExplorerProvider(mediaLibrary);
        break;
      case MediaType.ftp:
        break;
      case MediaType.smb:
        break;
      case MediaType.local:
        provider = LocalFileExplorerProvider();
        break;
    }
    _fileExplorerService.setProvider(provider, mediaLibrary.url);
    setState(() {
      _mediaLibrary = mediaLibrary;
    });
  }

  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      File image = File('${directory.path}/screenshots/${history.uniqueKey}');
      if (await image.exists()) {
        return Image.file(image, fit: BoxFit.fill);
      }
    }
    return const Icon(FIcons.play);
  }

  void _playVideo(FileItem file, int index) {
    final videoPath = '${_mediaLibrary!.url}${file.path}';
    _globalPlayerService.currentVideoPath = videoPath;
    final headers = jsonDecode(_mediaLibrary!.headers) as Map<String, dynamic>;
    _globalPlayerService.headers['Authorization'] = headers['Authorization'];
    effect(() {
      _globalPlayerService.currentIndex.value = index;
    });
    final location = Uri(path: videoPlayerPath);
    context.push(location.toString()).then((_) => _refresh());
  }

  Future<void> _refresh() async {
    await _fileExplorerService.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (!_fileExplorerService.goBack()) {
            Navigator.of(context).pop();
          }
        }
      },
      child: FScaffold(
        scaffoldStyle: context.theme.scaffoldStyle.copyWith(
          childPadding: EdgeInsets.all(0),
        ),
        header: FHeader(
          title: Row(
            children: [
              FButton.icon(
                style: FButtonStyle.ghost,
                onPress: () {
                  if (!_fileExplorerService.goBack()) {
                    Navigator.of(context).pop();
                  }
                },
                child: const Icon(FIcons.arrowLeft, size: 24),
              ),
              FButton.icon(
                style: FButtonStyle.ghost,
                onPress: Navigator.of(context).pop,
                child: const Icon(FIcons.x, size: 24),
              ),
              SizedBox(width: 10),
              _mediaLibrary == null
                  ? const Text('加载中...')
                  : Text(_mediaLibrary!.name),
            ],
          ),
        ),
        child:
            _mediaLibrary == null
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                  onRefresh: _refresh,
                  child: Watch(
                    (context) => _fileExplorerService.files.value.map(
                      data: (files) {
                        if (files.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  FIcons.folder,
                                  size: 48,
                                  color: context.theme.colors.mutedForeground,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  '此文件夹为空',
                                  style: context.theme.typography.lg,
                                ),
                              ],
                            ),
                          );
                        }
                        return CustomScrollView(
                          slivers: [
                            SliverToBoxAdapter(
                              child: FTileGroup(
                                divider: FTileDivider.indented,
                                style: tileGroupStyle(
                                  colors: context.theme.colors,
                                  typography: context.theme.typography,
                                  style: context.theme.style,
                                  newColors: context.theme.colors.copyWith(
                                    border: const Color.fromARGB(
                                      0,
                                      238,
                                      238,
                                      238,
                                    ),
                                  ),
                                ),
                                children: _listBuilder(files),
                              ),
                            ),
                          ],
                        );
                      },
                      error:
                          (error, stack) => const Center(child: Text('加载失败')),
                      loading:
                          () =>
                              const Center(child: CircularProgressIndicator()),
                    ),
                  ),
                ),
      ),
    );
  }

  List<FTileMixin> _listBuilder(List<FileItem> files) {
    final widgetList = <FTileMixin>[];
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      if (file.isFolder) {
        widgetList.add(
          FTile(
            prefixIcon: const Icon(FIcons.folder),
            title: Text(file.name),
            onPress: () => {_fileExplorerService.goDir(file.name)},
          ),
        );
        continue;
      }
      widgetList.add(
        FTile(
          style: videoTileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
            newColors: context.theme.colors,
          ),
          prefixIcon: SizedBox(
            width: 90,
            height: 60,
            child: FutureBuilder(
              future: _buildPerfix(file.history),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }
                if (snapshot.hasData) {
                  return file.isVideo
                      ? snapshot.data!
                      : const Icon(FIcons.file);
                }
                return const Icon(FIcons.file);
              },
            ),
          ),
          title: Text(file.name, maxLines: 2),
          subtitle:
              file.history != null
                  ? Text(
                    '观看进度: ${formatTime(file.history!.position, file.history!.duration)}',
                  )
                  : Text(''),
          onPress: () => _playVideo(file, i),
        ),
      );
    }
    return widgetList;
  }
}
