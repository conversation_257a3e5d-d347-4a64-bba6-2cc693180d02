import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:drift/drift.dart' hide Column;

class EditMediaLibraryPage extends StatefulWidget {
  final int? id;

  const EditMediaLibraryPage({super.key, this.id});

  @override
  State<EditMediaLibraryPage> createState() => _EditMediaLibraryPageState();
}

class _EditMediaLibraryPageState extends State<EditMediaLibraryPage> {
  final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
  final _formKey = GlobalKey<FormState>();
  var _mediaLibrary = MediaLibraryExtension.create();
  final _nameController = TextEditingController();
  final _urlController = TextEditingController();
  final _accountController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMediaLibrary();
  }

  Future<void> _loadMediaLibrary() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();

    if (widget.id != null) {
      final result = await mediaLibraryService.getMediaLibrary(widget.id!);
      if (result != null) {
        _mediaLibrary = result;
      }
    }

    setState(() {
      _nameController.text = _mediaLibrary.name;
      _urlController.text = _mediaLibrary.url;
      _accountController.text = _mediaLibrary.account ?? '';
      _passwordController.text = _mediaLibrary.password ?? '';
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<bool> _saveMediaLibrary(BuildContext context) async {
    if (!_formKey.currentState!.validate()) {
      return false;
    }

    setState(() => _isLoading = true);

    try {
      final updatedLibrary = _mediaLibrary.copyWith(
        name: _nameController.text.trim(),
        url: _urlController.text.trim(),
        account:
            _accountController.text.trim().isEmpty
                ? const Value.absent()
                : Value(_accountController.text.trim()),
        password:
            _passwordController.text.isEmpty
                ? const Value.absent()
                : Value(_passwordController.text),
      );
      await mediaLibraryService.updateMediaLibrary(updatedLibrary);

      if (context.mounted) {
        showtoast(true, '媒体库保存成功');
        _nameController.clear();
        _urlController.clear();
        _accountController.clear();
        _passwordController.clear();
      }
      return true;
    } catch (e) {
      if (context.mounted) {
        showtoast(false, '$e');
      }
      return false;
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String? _validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName不能为空';
    }
    return null;
  }

  String? _validateUrl(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'URL不能为空';
    }

    final uri = Uri.tryParse(value.trim());
    if (uri == null || !uri.hasScheme) {
      return '请输入有效的URL';
    }

    return null;
  }

  void showtoast(bool success, String message) {
    showFToast(
      context: context,
      title: success ? const Text('保存成功') : const Text('保存失败'),
      description: Text(message),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FScaffold(
      header: FHeader(title: const Text('编辑媒体库')),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            children: [
              FTextFormField(
                label: const Text('名称'),
                controller: _nameController,
                validator: (value) => _validateRequired(value, '名称'),
              ),
              const SizedBox(height: 10),
              FTextFormField(
                label: const Text('URL'),
                controller: _urlController,
                validator: _validateUrl,
              ),
              const SizedBox(height: 10),
              FTextFormField(
                label: const Text('账号'),
                controller: _accountController,
                enabled: !_mediaLibrary.isAnonymous,
                validator:
                    (value) =>
                        !_mediaLibrary.isAnonymous
                            ? _validateRequired(value, '账号')
                            : null,
              ),
              const SizedBox(height: 10),
              FTextFormField(
                label: const Text('密码'),
                obscureText: true,
                controller: _passwordController,
                enabled: !_mediaLibrary.isAnonymous,
                validator:
                    (value) =>
                        !_mediaLibrary.isAnonymous
                            ? _validateRequired(value, '密码')
                            : null,
              ),
              const SizedBox(height: 10),
              FSwitch(
                label: const Text('匿名访问'),
                value: _mediaLibrary.isAnonymous,
                onChange: (value) {
                  setState(() {
                    _mediaLibrary = _mediaLibrary.copyWith(isAnonymous: value);
                    if (value) {
                      _accountController.clear();
                      _passwordController.clear();
                      _formKey.currentState?.validate();
                    }
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: FButton(
                      style: context.theme.buttonStyles.secondary,
                      onPress: () => Navigator.of(context).pop(),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: FButton(
                      onPress:
                          _isLoading
                              ? null
                              : () async {
                                final result = await _saveMediaLibrary(context);
                                if (context.mounted) {
                                  if (result) {
                                    Navigator.of(context).pop(result);
                                  }
                                }
                              },
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('保存'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
