import 'package:flutter/material.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';
import 'package:screen_brightness/screen_brightness.dart';

enum IndicatorType { brightness, volume, speed }

/// 通用状态指示器
/// 用于显示亮度、音量、播放速度
class StatusIndicator extends StatefulWidget {
  final IndicatorType type;
  final double value;
  final bool isVisible;
  final Duration visibilityDuration;

  const StatusIndicator({
    super.key,
    required this.type,
    required this.value,
    required this.isVisible,
    this.visibilityDuration = const Duration(seconds: 2),
  });

  @override
  State<StatusIndicator> createState() => _StatusIndicatorState();
}

class _StatusIndicatorState extends State<StatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(StatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(opacity: _fadeAnimation.value, child: _buildIndicator());
      },
    );
  }

  Widget _buildIndicator() {
    IconData icon;
    String text;

    switch (widget.type) {
      case IndicatorType.brightness:
        icon = _getBrightnessIcon(widget.value);
        text = '${(widget.value * 100).round()}%';
        break;
      case IndicatorType.volume:
        icon = _getVolumeIcon(widget.value);
        text = '${(widget.value * 100).round()}%';
        break;
      case IndicatorType.speed:
        icon = Icons.speed;
        text = '${widget.value}x';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getBrightnessIcon(double brightness) {
    if (brightness < 0.3) {
      return Icons.brightness_low;
    } else if (brightness < 0.7) {
      return Icons.brightness_medium;
    } else {
      return Icons.brightness_high;
    }
  }

  IconData _getVolumeIcon(double volume) {
    if (volume == 0) {
      return Icons.volume_off;
    } else if (volume < 0.5) {
      return Icons.volume_down;
    } else {
      return Icons.volume_up;
    }
  }
}

/// 亮度控制服务
class BrightnessVolumeService {
  static double _currentBrightness = 0.5;
  static double _systemBrightness = 0.5;

  /// 获取当前亮度
  static double get currentBrightness => _currentBrightness;

  /// 设置亮度
  static Future<void> setBrightness(double brightness) async {
    brightness = brightness.clamp(0.0, 1.0);
    _currentBrightness = brightness;

    try {
      await ScreenBrightness().setApplicationScreenBrightness(brightness);
    } catch (e) {
      debugPrint('设置亮度失败: $e');
    }
  }

  /// 重置为系统亮度
  static Future<void> resetToSystemBrightness() async {
    try {
      await ScreenBrightness().resetApplicationScreenBrightness();
      _currentBrightness = _systemBrightness;
    } catch (e) {
      debugPrint('重置亮度失败: $e');
    }
  }

  static Future<void> initialize() async {
    try {
      _systemBrightness = await ScreenBrightness().system;
      _currentBrightness = await ScreenBrightness().application;
      _currentVolume = await FlutterVolumeController.getVolume() ?? 0.5;

      FlutterVolumeController.addListener((volume) {
        _currentVolume = volume;
      });
    } catch (e) {
      debugPrint('初始化亮度音量服务失败: $e');
      _systemBrightness = 0.5;
      _currentBrightness = 0.5;
    }
  }

  static double _currentVolume = 0.5;

  /// 获取当前音量
  static double get currentVolume => _currentVolume;

  /// 设置音量
  static Future<void> setVolume(double volume) async {
    volume = volume.clamp(0.0, 1.0);
    _currentVolume = volume;

    try {
      await FlutterVolumeController.setVolume(volume);
    } catch (e) {
      debugPrint('设置音量失败: $e');
    }
  }

  /// 释放资源
  static void dispose() {
    resetToSystemBrightness();
    FlutterVolumeController.removeListener();
  }
}
