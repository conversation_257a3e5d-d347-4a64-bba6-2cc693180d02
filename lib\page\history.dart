import 'dart:convert';
import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/theme/tile_group_style.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:path/path.dart' as path;
import 'package:signals_flutter/signals_flutter.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({super.key});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  final HistoryService _historyService = GetIt.I.get<HistoryService>();
  final MediaLibraryService _mediaLibraryService = GetIt.I.get<MediaLibraryService>();
  final FileExplorerService _fileExplorerService = GetIt.I.get<FileExplorerService>();
  final GlobalPlayerService _globalPlayerService = GetIt.I.get<GlobalPlayerService>();
  
  final Signal<bool> _isLoading = signal(true);
  final Signal<String?> _error = signal(null);
  final Signal<List<History>> _histories = signal([]);
  
  @override
  void initState() {
    super.initState();
    _loadHistories();
  }
  
  Future<void> _loadHistories() async {
    _isLoading.value = true;
    _error.value = null;
    
    try {
      final histories = await _historyService.getAllHistories();
      // 按最后观看时间倒序排列
      histories.sort((a, b) => b.updateTime.compareTo(a.updateTime));
      _histories.value = histories;
    } catch (e) {
      _error.value = '加载历史记录失败: $e';
    } finally {
      _isLoading.value = false;
    }
  }
  
  // 从URL中提取文件名
  String _extractFileName(String url) {
    try {
      final fileName = path.basename(url);
      return fileName;
    } catch (e) {
      return url.split('/').last;
    }
  }
  
  // 格式化最后观看时间
  String _formatLastWatchTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}年前';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  // 判断是否为媒体库视频
  bool _isMediaLibraryVideo(String url) {
    // 检查URL是否包含媒体库的URL前缀
    for (final mediaLibrary in _mediaLibraryService.mediaLibraries.value) {
      if (url.startsWith(mediaLibrary.url)) {
        return true;
      }
    }
    return false;
  }
  
  // 获取媒体库ID
  Future<MediaLibrary?> _getMediaLibraryFromUrl(String url) async {
    for (final mediaLibrary in _mediaLibraryService.mediaLibraries.value) {
      if (url.startsWith(mediaLibrary.url)) {
        return mediaLibrary;
      }
    }
    return null;
  }
  
  // 播放视频
  Future<void> _playVideo(History history) async {
    try {
      if (_isMediaLibraryVideo(history.url)) {
        // 媒体库视频需要额外处理
        final mediaLibrary = await _getMediaLibraryFromUrl(history.url);
        if (mediaLibrary == null) {
          _error.value = '找不到对应的媒体库';
          return;
        }
        
        // 设置FileExplorerService的provider和rootPath
        switch (mediaLibrary.mediaType) {
          case MediaType.webdav:
            final provider = WebDavFileExplorerProvider(
              url: mediaLibrary.url,
              headers: mediaLibrary.headers,
            );
            _fileExplorerService.setProvider(provider, mediaLibrary.url);
            break;
          case MediaType.local:
            final provider = LocalFileExplorerProvider();
            _fileExplorerService.setProvider(provider, mediaLibrary.url);
            break;
          default:
            _error.value = '不支持的媒体库类型';
            return;
        }
        
        // 设置GlobalPlayerService的参数
        _globalPlayerService.currentVideoPath = history.url;
        final headers = jsonDecode(history.headers) as Map<String, dynamic>;
        _globalPlayerService.headers['Authorization'] = headers['Authorization'];
        
        // 导航到视频播放页面
        final location = Uri(path: videoPlayerPath);
        context.push(location.toString());
      } else {
        // 本地视频或其他类型的视频
        _globalPlayerService.currentVideoPath = history.url;
        _globalPlayerService.headers.clear();
        
        // 导航到视频播放页面
        final location = Uri(path: videoPlayerPath);
        context.push(location.toString());
      }
    } catch (e) {
      _error.value = '播放视频失败: $e';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('观看历史'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadHistories,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadHistories,
        child: Watch((context) {
          if (_isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (_error.value != null) {
            return Center(child: Text(_error.value!));
          }
          
          if (_histories.value.isEmpty) {
            return const Center(child: Text('暂无观看历史'));
          }
          
          return ListView.builder(
            itemCount: _histories.value.length,
            itemBuilder: (context, index) {
              final history = _histories.value[index];
              final fileName = _extractFileName(history.url);
              final lastWatchTime = _formatLastWatchTime(history.updateTime);
              
              return FTile(
                style: videoTileStyle(
                  colors: context.theme.colors,
                  typography: context.theme.typography,
                  style: context.theme.style,
                  newColors: context.theme.colors,
                ),
                title: Text(fileName, maxLines: 2),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '观看进度: ${formatTime(history.position, history.duration)}',
                    ),
                    Text('最后观看: $lastWatchTime'),
                  ],
                ),
                onPress: () => _playVideo(history),
              );
            },
          );
        }),
      ),
    );
  }
}
