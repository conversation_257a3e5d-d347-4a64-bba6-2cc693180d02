import 'dart:async';
import 'package:flutter/foundation.dart';

/// 播放器定时器管理器
/// 统一管理所有定时器任务，实现智能调度和资源优化
class PlayerTimerManager {
  Timer? _mainTimer;
  final List<Function()> _tasks = [];
  bool _isPlaying = false;

  /// 当前调度间隔
  Duration get _currentInterval {
    // 播放时3秒间隔，暂停时30秒间隔
    return _isPlaying
        ? const Duration(seconds: 3)
        : const Duration(seconds: 30);
  }

  /// 添加定时器任务
  void scheduleTask(Function() task) {
    // 添加新任务
    _tasks.add(task);
    debugPrint('PlayerTimerManager: 添加任务，当前任务数: ${_tasks.length}');

    // 重新调度
    _reschedule();
  }

  /// 移除指定类型的任务
  void removeTask(Function() task) {
    final removedCount = _tasks.length;
    _tasks.remove(task);
    final currentCount = _tasks.length;

    if (removedCount != currentCount) {
      debugPrint('PlayerTimerManager: 移除任务');
      _reschedule();
    }
  }

  /// 更新播放状态
  void updatePlayingState(bool isPlaying) {
    if (_isPlaying == isPlaying) return;

    _isPlaying = isPlaying;
    debugPrint(
      'PlayerTimerManager: 播放状态变更为 ${isPlaying ? "播放" : "暂停"}，调整调度间隔为 ${_currentInterval.inSeconds}秒',
    );

    // 重新调度以应用新的间隔
    _reschedule();
  }

  /// 重新调度定时器
  void _reschedule() {
    // 取消现有定时器
    _mainTimer?.cancel();
    _mainTimer = null;

    // 如果没有任务，不启动定时器
    if (_tasks.isEmpty) {
      debugPrint('PlayerTimerManager: 无任务，停止定时器');
      return;
    }

    // 启动新的定时器
    _mainTimer = Timer.periodic(_currentInterval, _executeTasks);
    debugPrint(
      'PlayerTimerManager: 重新调度定时器，间隔: ${_currentInterval.inSeconds}秒，任务数: ${_tasks.length}',
    );
  }

  /// 执行所有任务
  Future<void> _executeTasks(Timer timer) async {
    if (_tasks.isEmpty) return;

    for (final task in _tasks) {
      try {
        await task();
      } catch (e) {
        debugPrint('PlayerTimerManager: 任务执行失败: $e');
        // 继续执行其他任务，不因单个任务失败而中断
      }
    }
  }

  /// 立即执行所有任务（用于手动触发）
  Future<void> executeTasksImmediately() async {
    if (_tasks.isEmpty) return;

    debugPrint('PlayerTimerManager: 立即执行所有任务');
    await _executeTasks(_mainTimer!);
  }

  /// 获取当前任务数量
  int get taskCount => _tasks.length;

  /// 获取当前调度间隔（秒）
  int get currentIntervalSeconds => _currentInterval.inSeconds;

  /// 是否正在运行
  bool get isRunning => _mainTimer?.isActive == true;

  /// 释放资源
  void dispose() {
    debugPrint('PlayerTimerManager: 开始释放资源');

    _mainTimer?.cancel();
    _mainTimer = null;
    _tasks.clear();

    debugPrint('PlayerTimerManager: 资源释放完成');
  }
}
