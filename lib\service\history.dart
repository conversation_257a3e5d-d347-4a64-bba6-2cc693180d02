import 'dart:async';

import 'package:dandanplay_flutter/service/storage.dart';
import 'package:get_it/get_it.dart';
import 'package:drift/drift.dart';
import 'package:flutter/foundation.dart';

import '../utils/crypto_utils.dart';

/// 历史记录缓存管理器
/// 提供内存缓存功能，减少数据库查询次数
class HistoryCache {
  final Map<String, History> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// 获取缓存的历史记录
  History? getCachedHistory(String uniqueKey) {
    final timestamp = _cacheTimestamps[uniqueKey];
    if (timestamp == null) return null;

    // 检查缓存是否过期
    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _cache.remove(uniqueKey);
      _cacheTimestamps.remove(uniqueKey);
      return null;
    }

    return _cache[uniqueKey];
  }

  /// 缓存历史记录
  void cacheHistory(History history) {
    _cache[history.uniqueKey] = history;
    _cacheTimestamps[history.uniqueKey] = DateTime.now();
  }

  /// 更新缓存中的历史记录
  void updateCachedHistory(String uniqueKey, History updatedHistory) {
    if (_cache.containsKey(uniqueKey)) {
      _cache[uniqueKey] = updatedHistory;
      _cacheTimestamps[uniqueKey] = DateTime.now();
    }
  }

  /// 清除指定的缓存
  void clearCache(String uniqueKey) {
    _cache.remove(uniqueKey);
    _cacheTimestamps.remove(uniqueKey);
  }

  /// 清除所有缓存
  void clearAllCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// 获取缓存大小
  int get cacheSize => _cache.length;
}

/// 延迟更新管理器
/// 管理历史记录的延迟写入，减少频繁的数据库操作
class DelayedUpdateManager {
  final Map<int, _PendingUpdate> _pendingUpdates = {};
  Timer? _flushTimer;
  final Duration _flushInterval;
  final int _positionThreshold; // 位置变化阈值（毫秒）

  DelayedUpdateManager({Duration? flushInterval, int? positionThreshold})
    : _flushInterval = flushInterval ?? const Duration(seconds: 2),
      _positionThreshold = positionThreshold ?? 5000; // 默认5秒

  /// 添加待更新的记录
  void addPendingUpdate({
    required int historyId,
    required Duration position,
    required Duration duration,
  }) {
    final existing = _pendingUpdates[historyId];
    final newUpdate = _PendingUpdate(
      historyId: historyId,
      position: position,
      duration: duration,
      timestamp: DateTime.now(),
    );

    // 检查是否需要立即更新（位置变化超过阈值）
    bool shouldFlushImmediately = false;
    if (existing != null) {
      final positionDiff =
          (position.inMilliseconds - existing.position.inMilliseconds).abs();
      shouldFlushImmediately = positionDiff >= _positionThreshold;
    }

    _pendingUpdates[historyId] = newUpdate;

    if (shouldFlushImmediately) {
      _flushSingleUpdate(historyId);
    } else {
      _scheduleFlush();
    }
  }

  /// 调度刷新操作
  void _scheduleFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer(_flushInterval, () {
      flushAllUpdates();
    });
  }

  /// 刷新单个更新
  Future<void> _flushSingleUpdate(int historyId) async {
    final update = _pendingUpdates.remove(historyId);
    if (update != null) {
      await _executeUpdate(update);
    }
  }

  /// 刷新所有待更新的记录
  Future<void> flushAllUpdates() async {
    if (_pendingUpdates.isEmpty) return;

    final updates = List<_PendingUpdate>.from(_pendingUpdates.values);
    _pendingUpdates.clear();
    _flushTimer?.cancel();

    for (final update in updates) {
      await _executeUpdate(update);
    }
  }

  /// 执行更新操作
  Future<void> _executeUpdate(_PendingUpdate update) async {
    try {
      final historyService = GetIt.I.get<HistoryService>();
      await historyService._performDirectUpdate(
        id: update.historyId,
        position: update.position,
        duration: update.duration,
      );
    } catch (e) {
      debugPrint('DelayedUpdateManager: 更新历史记录失败: $e');
      // 可以在这里实现重试机制
    }
  }

  /// 获取待更新记录数量
  int get pendingCount => _pendingUpdates.length;

  /// 释放资源
  void dispose() {
    _flushTimer?.cancel();
    _pendingUpdates.clear();
  }
}

/// 待更新记录
class _PendingUpdate {
  final int historyId;
  final Duration position;
  final Duration duration;
  final DateTime timestamp;

  _PendingUpdate({
    required this.historyId,
    required this.position,
    required this.duration,
    required this.timestamp,
  });
}

class HistoryService {
  final StorageService storage;

  // 缓存和延迟更新管理器
  late final HistoryCache _cache;
  late final DelayedUpdateManager _delayedUpdateManager;

  HistoryService({required this.storage}) {
    _cache = HistoryCache();
    _delayedUpdateManager = DelayedUpdateManager();
  }

  static Future<void> register() async {
    final service = HistoryService(storage: GetIt.I.get<StorageService>());
    GetIt.I.registerSingleton<HistoryService>(service);
  }

  /// 释放资源
  void dispose() {
    _delayedUpdateManager.dispose();
    _cache.clearAllCache();
  }

  Future<History?> getHistoryByUniqueKey(String uniqueKey) async {
    // 首先尝试从缓存获取
    final cachedHistory = _cache.getCachedHistory(uniqueKey);
    if (cachedHistory != null) {
      debugPrint('HistoryService: 从缓存获取历史记录 $uniqueKey');
      return cachedHistory;
    }

    // 缓存未命中，从数据库查询
    final history = await storage.getHistoryByUniqueKey(uniqueKey);
    if (history != null) {
      // 缓存查询结果
      _cache.cacheHistory(history);
      debugPrint('HistoryService: 从数据库查询并缓存历史记录 $uniqueKey');
    }

    return history;
  }

  Future<List<History>> getAllHistories() async {
    return await storage.getHistories();
  }

  Future<void> clearAllHistories() async {
    await storage.clearAllHistories();
  }

  Future<void> deleteHistory(int id) async {
    await storage.deleteHistory(id);
  }

  /// 开始记录播放历史
  Future<void> addHistory({
    required String url,
    required String headers,
    required Duration duration,
  }) async {
    // 生成唯一键
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(url);

    // 检查是否已存在历史记录
    final existingHistory = await getHistoryByUniqueKey(uniqueKey);

    if (existingHistory == null) {
      // 创建新的历史记录
      final history = HistoriesCompanion.insert(
        uniqueKey: uniqueKey,
        duration: duration.inMilliseconds,
        position: 0,
        url: url,
        headers: headers,
        updateTime: DateTime.now().millisecondsSinceEpoch,
        danmakuUpdateTime: 0,
      );
      await storage.createHistory(history);

      // 重新查询以获取正确的ID，然后缓存
      final createdHistory = await storage.getHistoryByUniqueKey(uniqueKey);
      if (createdHistory != null) {
        _cache.cacheHistory(createdHistory);
        debugPrint('HistoryService: 创建并缓存新历史记录 $uniqueKey');
      }
    } else {
      // 更新历史记录
      final companion = HistoriesCompanion(
        id: Value(existingHistory.id),
        headers: Value(headers),
        duration: Value(duration.inMilliseconds),
        updateTime: Value(DateTime.now().millisecondsSinceEpoch),
      );
      await storage.updateHistory(companion);

      // 更新缓存
      final updatedHistory = History(
        id: existingHistory.id,
        uniqueKey: existingHistory.uniqueKey,
        duration: duration.inMilliseconds,
        position: existingHistory.position,
        url: existingHistory.url,
        headers: headers,
        updateTime: DateTime.now().millisecondsSinceEpoch,
        danmakuUpdateTime: existingHistory.danmakuUpdateTime,
      );
      _cache.updateCachedHistory(uniqueKey, updatedHistory);
      debugPrint('HistoryService: 更新并缓存历史记录 $uniqueKey');
    }
  }

  /// 更新播放进度（使用延迟更新机制）
  Future<void> updateProgress({
    required Duration position,
    required Duration duration,
    required int id,
  }) async {
    // 使用延迟更新管理器，减少频繁的数据库操作
    _delayedUpdateManager.addPendingUpdate(
      historyId: id,
      position: position,
      duration: duration,
    );

    // 同时更新缓存中的记录
    await _updateCacheForProgress(id, position, duration);
  }

  /// 直接执行数据库更新（内部方法，供DelayedUpdateManager调用）
  Future<void> _performDirectUpdate({
    required int id,
    required Duration position,
    required Duration duration,
  }) async {
    final companion = HistoriesCompanion(
      id: Value(id),
      position: Value(position.inMilliseconds),
      duration: Value(duration.inMilliseconds),
      updateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );

    await storage.updateProgress(companion);

    // 更新缓存
    await _updateCacheForProgress(id, position, duration);
  }

  /// 更新缓存中的进度信息
  Future<void> _updateCacheForProgress(
    int id,
    Duration position,
    Duration duration,
  ) async {
    // 查找缓存中对应的记录并更新
    for (final entry in _cache._cache.entries) {
      final history = entry.value;
      if (history.id == id) {
        final updatedHistory = History(
          id: history.id,
          uniqueKey: history.uniqueKey,
          duration: duration.inMilliseconds,
          position: position.inMilliseconds,
          url: history.url,
          headers: history.headers,
          updateTime: DateTime.now().millisecondsSinceEpoch,
          danmakuUpdateTime: history.danmakuUpdateTime,
        );
        _cache.updateCachedHistory(history.uniqueKey, updatedHistory);
        break;
      }
    }
  }

  /// 保存弹幕路径
  Future<void> saveDanmakuUpdateTime({required String videoPath}) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    final companion = HistoriesCompanion(
      danmakuUpdateTime: Value(DateTime.now().millisecondsSinceEpoch),
    );
    await (storage.update(storage.histories)
      ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).write(companion);
  }

  /// 获取播放历史
  Future<History?> getPlaybackHistory(String videoPath) async {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    return await getHistoryByUniqueKey(uniqueKey);
  }

  /// 强制刷新所有待更新的记录
  /// 在应用退出或重要时刻调用，确保数据不丢失
  Future<void> flushPendingUpdates() async {
    await _delayedUpdateManager.flushAllUpdates();
    debugPrint('HistoryService: 强制刷新所有待更新记录完成');
  }

  /// 清除指定视频的缓存
  void clearVideoCache(String videoPath) {
    final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
    _cache.clearCache(uniqueKey);
    debugPrint('HistoryService: 清除视频缓存 $uniqueKey');
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'cacheSize': _cache.cacheSize,
      'pendingUpdates': _delayedUpdateManager.pendingCount,
    };
  }

  /// 获取播放进度百分比（优化版本，使用缓存）
  Future<double> getProgressPercentage(String videoPath) async {
    final history = await getPlaybackHistory(videoPath);
    if (history == null || history.duration <= 0) {
      return 0.0;
    }
    return history.position / history.duration;
  }

  /// 判断视频是否已观看完成（优化版本，使用缓存）
  Future<bool> isVideoCompleted(String videoPath) async {
    final progressPercentage = await getProgressPercentage(videoPath);
    return progressPercentage >= 0.9; // 观看90%以上认为已完成
  }
}
