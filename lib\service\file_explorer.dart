import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/service/history.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/crypto_utils.dart';
import 'package:get_it/get_it.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:webdav_client/webdav_client.dart';

abstract class FileExplorerProvider {
  Future<List<FileItem>> listFiles(String path, String rootPath);
}

class FileExplorerService {
  final Signal<FileExplorerProvider?> provider = signal(null);
  final path = signal('/');
  String? rootPath;
  final Signal<String?> error = signal(null);

  late final StreamSignal<List<FileItem>> files = streamSignal(() async* {
    if (provider.value == null || rootPath == null) {
      yield [];
    }
    final list = await provider.value!.listFiles(path.value, rootPath!);
    yield list;
  }, dependencies: [path, provider]);

  static void register() {
    final service = FileExplorerService();
    GetIt.I.registerSingleton<FileExplorerService>(service);
  }

  void setProvider(FileExplorerProvider newProvider, String newRootPath) {
    provider.value = newProvider;
    rootPath = newRootPath;
    path.value = '/';
  }

  void goDir(String name) {
    path.value = '${path.value}/$name';
  }

  bool goBack() {
    if (path.value == '/') {
      return false;
    }
    path.value = path.value
        .split('/')
        .sublist(0, path.value.split('/').length - 1)
        .join('/');
    return true;
  }

  String? switchVideo(int index) {
    final asyncList = files.value;
    if (asyncList.hasValue) {
      final list = asyncList.requireValue;
      if (index >= list.length || index < 0 || list[index].isFolder) {
        return null;
      }
      return rootPath! + list[index].path;
    }
    return null;
  }

  Future<void> refresh() async {
    await files.refresh();
  }
}

// WebDAV implementation (placeholder)
class WebDAVFileExplorerProvider implements FileExplorerProvider {
  late Client client;

  WebDAVFileExplorerProvider(MediaLibrary mediaLibrary) {
    if (mediaLibrary.isAnonymous) {
      client = newClient(mediaLibrary.url);
    } else {
      client = newClient(
        mediaLibrary.url,
        user: mediaLibrary.account!,
        password: mediaLibrary.password!,
      );
    }
  }

  @override
  Future<List<FileItem>> listFiles(String path, String rootPath) async {
    List<FileItem> list = [];
    var fileList = await client.readDir(path);
    for (var file in fileList) {
      if (file.name == null || file.path == null) {
        continue;
      }
      if (FileItem.getFileType(file.name!) != FileType.video && !file.isDir!) {
        continue;
      }
      if (file.isDir!) {
        list.add(
          FileItem(name: file.name!, path: file.path!, type: FileType.folder),
        );
        continue;
      }
      var uniqueKey = CryptoUtils.generateVideoUniqueKey(
        '$rootPath${file.path}',
      );
      var history = await GetIt.I.get<HistoryService>().getHistoryByUniqueKey(
        uniqueKey,
      );
      list.add(
        FileItem(
          name: file.name!,
          path: file.path!,
          type: FileItem.getFileType(file.name!),
          size: file.size,
          uniqueKey: uniqueKey,
          history: history,
        ),
      );
    }
    // 根据文件名排序，同时文件夹放在最前面
    list.sort((a, b) {
      if (a.isFolder && !b.isFolder) {
        return -1;
      }
      if (!a.isFolder && b.isFolder) {
        return 1;
      }
      return a.name.compareTo(b.name);
    });
    return list;
  }
}

// Local file system implementation (placeholder)
class LocalFileExplorerProvider implements FileExplorerProvider {
  @override
  Future<List<FileItem>> listFiles(String path, String rootPath) async {
    // TODO: Implement local file system listing
    return [
      FileItem(
        name: 'Local Folder',
        path: '$path/Local Folder',
        type: FileType.folder,
      ),
      FileItem(
        name: 'local_video.mp4',
        path: '$path/local_video.mp4',
        type: FileType.video,
        size: 1024 * 1024 * 200, // 200MB
      ),
    ];
  }
}
