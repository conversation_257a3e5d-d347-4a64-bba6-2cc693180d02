# 文件浏览器功能实现文档

## 概述

本文档描述了为dandanplay_flutter项目实现的文件浏览器功能。该功能允许用户浏览媒体库中的文件和文件夹，并显示视频文件的观看历史信息。

## 实现的功能

### 1. 核心功能
- ✅ 文件和文件夹浏览
- ✅ 支持多种媒体库类型（WebDAV、FTP、SMB、本地文件）
- ✅ 视频文件识别和显示
- ✅ 观看历史集成（快照、观看进度）
- ✅ MD5唯一键生成
- ✅ 响应式UI设计

### 2. 技术特性
- ✅ 使用forui组件库
- ✅ 遵循tile_group_style.dart样式规范
- ✅ 可扩展的协议支持架构
- ✅ Drift数据库集成
- ✅ 类型安全的文件处理

## 文件结构

### 新增文件
```
lib/
├── model/
│   └── file_item.dart              # 文件项数据模型
├── service/
│   ├── file_explorer.dart          # 文件浏览器服务
│   └── history.dart                # 观看历史服务
├── utils/
│   └── crypto_utils.dart           # MD5计算工具
└── page/
    └── file_explorer.dart          # 文件浏览器页面

test/
└── file_explorer_test.dart         # 单元测试
```

### 修改的文件
```
lib/
├── service/
│   └── storage.dart                # 扩展MediaType枚举
├── page/root/
│   └── media_library.dart          # 添加跳转功能
├── router.dart                     # 添加文件浏览器路由
└── main.dart                       # 注册新服务
```

## 数据模型

### FileItem
```dart
class FileItem {
  final String name;           // 文件名
  final String path;           // 完整路径
  final FileType type;         // 文件类型（文件夹/视频/其他）
  final int? size;             // 文件大小
  final DateTime? modifiedTime; // 修改时间
  final String? uniqueKey;     // MD5唯一键（视频文件）
  final History? history;      // 关联的观看历史
}
```

### MediaType扩展
```dart
enum MediaType { 
  webdav,  // WebDAV协议
  ftp,     // FTP协议
  smb,     // SMB/CIFS协议
  local    // 本地文件系统
}
```

## 服务架构

### FileExplorerService
- 管理多个协议提供者
- 统一的文件访问接口
- 自动集成观看历史数据

### HistoryService
- 基于MD5唯一键的历史查询
- 观看进度和快照管理
- 数据库CRUD操作

### 协议提供者架构
```dart
abstract class FileExplorerProvider {
  Future<List<FileItem>> listFiles(String path);
  bool supports(MediaLibrary mediaLibrary);
}
```

## UI组件

### 文件浏览器页面特性
- 使用FTileGroup和tileGroupStyle保持一致性
- 支持文件夹导航（上级目录按钮）
- 视频文件显示观看进度
- 文件大小格式化显示
- 错误状态处理
- 加载状态指示

### 样式一致性
- 遵循MediaLibraryPage的设计模式
- 使用相同的tile_group_style.dart样式
- 统一的图标和颜色主题

## 路由配置

```dart
GoRoute(
  path: '/file-explorer',
  builder: (context, state) {
    final mediaLibraryId = int.tryParse(state.uri.queryParameters['id'] ?? '');
    final path = state.uri.queryParameters['path'] ?? '/';
    return FileExplorerPage(
      mediaLibraryId: mediaLibraryId,
      initialPath: path,
    );
  },
)
```

## 使用方法

### 从媒体库页面跳转
```dart
onPress: () {
  context.push('$fileExplorerPath?id=${library.id}&path=/');
}
```

### 视频文件唯一键生成
```dart
final uniqueKey = CryptoUtils.generateVideoUniqueKey(videoPath);
final history = await historyService.getHistoryByUniqueKey(uniqueKey);
```

## 扩展性

### 添加新协议支持
1. 实现FileExplorerProvider接口
2. 在FileExplorerService中注册提供者
3. 更新MediaType枚举（如需要）

### 自定义文件类型
1. 扩展FileType枚举
2. 更新FileItem.getFileType方法
3. 添加相应的UI处理逻辑

## 测试

项目包含完整的单元测试：
- 文件类型检测
- MD5计算功能
- 数据模型创建
- 枚举值验证

运行测试：
```bash
flutter test test/file_explorer_test.dart
```

## 待实现功能

### 协议实现
- [ ] WebDAV协议具体实现
- [ ] FTP协议支持
- [ ] SMB协议支持
- [ ] 本地文件系统访问

### 功能增强
- [ ] 视频播放集成
- [ ] 文件搜索功能
- [ ] 排序和过滤选项
- [ ] 缩略图显示
- [ ] 批量操作支持

## 依赖项

新增依赖：
- `crypto: ^3.0.6` - MD5计算

现有依赖：
- `forui` - UI组件库
- `drift` - 数据库ORM
- `get_it` - 依赖注入
- `go_router` - 路由管理

## 总结

文件浏览器功能已成功实现，提供了：
1. 完整的文件浏览体验
2. 与现有代码的良好集成
3. 可扩展的架构设计
4. 一致的UI/UX体验
5. 完整的测试覆盖

该实现为后续的协议具体实现和功能增强奠定了坚实的基础。
