import 'package:dio/dio.dart';
import '../model/danmaku.dart';

/// 弹弹play API工具类
class DanmakuApiUtils {
  static const String baseUrl =
      'https://ddplay-api.930524.xyz/cors/https://api.dandanplay.net';

  // TODO: 需要申请真实的AppId和AppSecret
  static const String appId = 'your_app_id';
  static const String appSecret = 'your_app_secret';

  static final Dio _dio = Dio(
    BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
    ),
  );

  /// 生成API签名
  // static String _generateSignature(String path, int timestamp) {
  //   final data = '$appId$timestamp$path$appSecret';
  //   final bytes = utf8.encode(data);
  //   final digest = sha256.convert(bytes);
  //   return base64.encode(digest.bytes);
  // }

  /// 获取请求头
  static Map<String, String> _getHeaders(String path) {
    // final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    // final signature = _generateSignature(path, timestamp);

    // return {
    //   'X-AppId': appId,
    //   'X-Timestamp': timestamp.toString(),
    //   'X-Signature': signature,
    //   'Content-Type': 'application/json',
    // };
    return {'Content-Type': 'application/json'};
  }

  /// 文件识别 - 根据文件信息匹配节目
  static Future<List<Episode>> matchVideo({
    required String fileName,
    required String fileHash,
    required int fileSize,
    required int duration,
  }) async {
    const path = '/api/v2/match';

    try {
      final response = await _dio.post(
        path,
        options: Options(headers: _getHeaders(path)),
        data: {
          'fileName': fileName,
          'fileHash': fileHash,
          'fileSize': fileSize,
          'videoDuration': duration,
        },
      );

      final matches = response.data['matches'] as List;
      return matches.map((match) => Episode.fromJson(match)).toList();
    } on DioException catch (e) {
      throw DanmakuApiException('网络请求失败: ${e.message}', -1);
    }
  }

  /// 搜索节目
  static Future<List<Episode>> searchAnime(String keyword) async {
    const path = '/api/v2/search/episodes';

    try {
      final response = await _dio.get(
        path,
        options: Options(headers: _getHeaders(path)),
        queryParameters: {'anime': keyword},
      );

      final animes = response.data['animes'] as List;
      final episodes = <Episode>[];

      for (final anime in animes) {
        final episodeList = anime['episodes'] as List;
        episodes.addAll(
          episodeList.map(
            (ep) =>
                Episode.fromJson({...ep, 'animeTitle': anime['animeTitle']}),
          ),
        );
      }

      return episodes;
    } on DioException catch (e) {
      throw DanmakuApiException('网络请求失败: ${e.message}', -1);
    }
  }

  /// 搜索番剧集数
  static Future<List<Anime>> searchEpisodes(String name) async {
    const path = '/api/v2/search/episodes';

    try {
      final queryParameters = <String, dynamic>{'anime': name};

      final response = await _dio.get(
        path,
        options: Options(headers: _getHeaders(path)),
        queryParameters: queryParameters,
      );

      final animes = <Anime>[];

      // 遍历所有番剧，收集所有集数
      for (final anime in response.data['animes'] as List) {
        animes.add(Anime.fromJson(anime));
      }

      return animes;
    } on DioException catch (e) {
      throw DanmakuApiException('网络请求失败: ${e.message}', -1);
    }
  }

  /// 获取弹幕
  static Future<List<DanmakuComment>> getComments(
    String episodeId, {
    bool withRelated = true,
  }) async {
    final path = '/api/v2/comment/$episodeId';

    try {
      final response = await _dio.get(
        path,
        options: Options(headers: _getHeaders(path)),
        queryParameters: {if (withRelated) 'withRelated': 'true'},
      );

      final comments = response.data['comments'] as List;
      return comments
          .map((comment) => DanmakuComment.fromJson(comment))
          .toList();
    } on DioException catch (e) {
      throw DanmakuApiException('网络请求失败: ${e.message}', -1);
    }
  }
}

/// 弹弹play API异常
class DanmakuApiException implements Exception {
  final String message;
  final int? errorCode;

  const DanmakuApiException(this.message, this.errorCode);

  @override
  String toString() {
    return 'DanmakuApiException: $message (code: $errorCode)';
  }
}
