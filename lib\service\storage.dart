import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:get_it/get_it.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

part 'storage.g.dart';

@DriftDatabase(tables: [MediaLibraries, Histories])
class StorageService extends _$StorageService {
  StorageService() : super(_openConnection());

  // 测试用构造函数
  StorageService.forTesting(DatabaseConnection super.connection);

  @override
  int get schemaVersion => 1;

  static Future<void> register() async {
    final db = StorageService();
    GetIt.I.registerSingleton<StorageService>(db);
  }

  Future<List<MediaLibrary>> getMediaLibraries() =>
      select(mediaLibraries).get();
  Future<MediaLibrary?> getMediaLibrary(int id) =>
      (select(mediaLibraries)
        ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  Future<int> createMediaLibrary(MediaLibrariesCompanion mediaLibrary) =>
      into(mediaLibraries).insert(mediaLibrary);
  Future<void> updateMediaLibrary(MediaLibrariesCompanion mediaLibrary) =>
      into(mediaLibraries).insertOnConflictUpdate(mediaLibrary);
  Future<void> deleteMediaLibrary(int id) =>
      (delete(mediaLibraries)..where((tbl) => tbl.id.equals(id))).go();

  Future<List<History>> getHistories() => select(histories).get();
  Future<History?> getHistory(int id) =>
      (select(histories)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  Future<History?> getHistoryByUniqueKey(String uniqueKey) =>
      (select(histories)
        ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).getSingleOrNull();
  Future<void> createHistory(HistoriesCompanion history) =>
      into(histories).insert(history);
  Future<void> updateHistory(HistoriesCompanion history) =>
      (update(histories)
        ..where((tbl) => tbl.id.equals(history.id.value))).write(history);
  Future<void> updateProgress(HistoriesCompanion history) =>
      (update(histories)
        ..where((tbl) => tbl.id.equals(history.id.value))).write(history);
  Future<void> deleteHistory(int id) =>
      (delete(histories)..where((tbl) => tbl.id.equals(id))).go();
  Future<void> clearAllHistories() => delete(histories).go();
}

enum MediaType { webdav, ftp, smb, local }

class Histories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get uniqueKey => text().unique()();
  IntColumn get duration => integer()();
  IntColumn get position => integer()();
  TextColumn get url => text()();
  TextColumn get headers => text()();
  IntColumn get updateTime => integer()();
  IntColumn get danmakuUpdateTime => integer()();
}

class MediaLibraries extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text()();
  TextColumn get url => text()();
  TextColumn get headers => text()();
  IntColumn get mediaType => intEnum<MediaType>()();
  TextColumn get account => text().nullable()();
  TextColumn get password => text().nullable()();
  BoolColumn get isAnonymous => boolean().withDefault(const Constant(false))();
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'db.sqlite'));
    return NativeDatabase.createInBackground(file);
  });
}
