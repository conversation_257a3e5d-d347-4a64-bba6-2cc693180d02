import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';

class CryptoUtils {
  /// Generate unique key for video file based on its path
  static String generateVideoUniqueKey(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 计算视频文件的MD5哈希值（前16MB数据）
  /// 用于弹弹play API的文件识别
  static Future<String> calculateVideoHash(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('文件不存在', filePath);
      }

      // 读取前16MB数据
      const int maxBytes = 16 * 1024 * 1024; // 16MB
      final fileSize = await file.length();
      final bytesToRead = fileSize < maxBytes ? fileSize : maxBytes;

      final randomAccessFile = await file.open();
      final bytes = await randomAccessFile.read(bytesToRead);
      await randomAccessFile.close();

      // 计算MD5
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      throw Exception('计算视频哈希值失败: $e');
    }
  }

  /// 计算网络视频的唯一标识
  /// 对于网络视频，使用URL作为标识
  static String calculateNetworkVideoHash(String url) {
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 计算网络视频的真实文件hash（前16MB数据）
  /// 用于弹弹play API的精确文件识别
  static Future<NetworkVideoHashResult> calculateNetworkVideoHashFromContent(
    String url, {
    Map<String, String>? headers,
    Function(int downloaded, int? total)? onProgress,
  }) async {
    final dio = Dio();
    const int maxBytes = 16 * 1024 * 1024; // 16MB

    try {
      // 首先尝试获取文件大小
      int? totalSize;
      try {
        final headResponse = await dio.head(
          url,
          options: Options(headers: headers),
        );
        final contentLength = headResponse.headers.value('content-length');
        if (contentLength != null) {
          totalSize = int.tryParse(contentLength);
        }
      } catch (e) {
        // HEAD请求失败，继续使用Range请求
      }

      // 使用Range请求下载前16MB
      final rangeHeaders = <String, String>{
        'Range': 'bytes=0-${maxBytes - 1}',
        ...?headers,
      };

      final response = await dio.get<List<int>>(
        url,
        options: Options(
          headers: rangeHeaders,
          responseType: ResponseType.bytes,
        ),
        onReceiveProgress: onProgress,
      );

      if (response.data == null) {
        throw Exception('响应数据为空');
      }

      final bytes = response.data!;
      final actualSize = bytes.length;

      // 检查是否支持Range请求
      final isRangeSupported = response.statusCode == 206;

      if (!isRangeSupported && actualSize > maxBytes) {
        // 服务器不支持Range请求，但文件太大，只取前16MB
        final truncatedBytes = bytes.take(maxBytes).toList();
        final digest = md5.convert(truncatedBytes);

        return NetworkVideoHashResult(
          hash: digest.toString(),
          downloadedBytes: maxBytes,
          totalFileSize: totalSize,
          isRangeSupported: false,
          isPartialHash: true,
        );
      } else {
        // 计算实际下载数据的hash
        final digest = md5.convert(bytes);

        return NetworkVideoHashResult(
          hash: digest.toString(),
          downloadedBytes: actualSize,
          totalFileSize: totalSize ?? actualSize,
          isRangeSupported: isRangeSupported,
          isPartialHash: actualSize < (totalSize ?? actualSize),
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 416) {
        // Range请求不支持，回退到URL hash
        return NetworkVideoHashResult.fallback(
          hash: calculateNetworkVideoHash(url),
          error: 'Range请求不支持，使用URL hash',
        );
      } else if (e.response?.statusCode == 405 ||
          e.response?.statusCode == 501) {
        // 方法不支持，回退到URL hash
        return NetworkVideoHashResult.fallback(
          hash: calculateNetworkVideoHash(url),
          error: 'Range请求方法不支持，使用URL hash',
        );
      } else {
        throw Exception('网络请求失败: ${e.message}');
      }
    } catch (e) {
      throw Exception('计算网络视频哈希值失败: $e');
    }
  }

  /// 生成弹弹play API签名
  static String generateApiSignature(
    String appId,
    int timestamp,
    String path,
    String appSecret,
  ) {
    final data = '$appId$timestamp$path$appSecret';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return base64.encode(digest.bytes);
  }
}

/// 网络视频hash计算结果
class NetworkVideoHashResult {
  /// 计算得到的hash值
  final String hash;

  /// 实际下载的字节数
  final int downloadedBytes;

  /// 文件总大小（如果已知）
  final int? totalFileSize;

  /// 是否支持Range请求
  final bool isRangeSupported;

  /// 是否为部分文件的hash
  final bool isPartialHash;

  /// 错误信息（降级情况）
  final String? error;

  /// 是否为降级结果（使用URL hash）
  final bool isFallback;

  const NetworkVideoHashResult({
    required this.hash,
    required this.downloadedBytes,
    this.totalFileSize,
    required this.isRangeSupported,
    required this.isPartialHash,
    this.error,
    this.isFallback = false,
  });

  /// 创建降级结果
  factory NetworkVideoHashResult.fallback({
    required String hash,
    required String error,
  }) {
    return NetworkVideoHashResult(
      hash: hash,
      downloadedBytes: 0,
      totalFileSize: null,
      isRangeSupported: false,
      isPartialHash: false,
      error: error,
      isFallback: true,
    );
  }

  /// 是否为成功的真实文件hash
  bool get isRealFileHash => !isFallback && error == null;

  /// 获取下载进度描述
  String get downloadProgressText {
    if (isFallback) return '使用URL标识';

    final downloadedMB = downloadedBytes / (1024 * 1024);
    if (totalFileSize != null) {
      final totalMB = totalFileSize! / (1024 * 1024);
      return '已下载 ${downloadedMB.toStringAsFixed(1)}MB / ${totalMB.toStringAsFixed(1)}MB';
    } else {
      return '已下载 ${downloadedMB.toStringAsFixed(1)}MB';
    }
  }

  /// 获取hash类型描述
  String get hashTypeText {
    if (isFallback) return 'URL哈希';
    if (isPartialHash) return '部分文件哈希';
    return '完整文件哈希';
  }
}
