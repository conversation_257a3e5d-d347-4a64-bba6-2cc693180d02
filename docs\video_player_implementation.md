# 视频播放器功能实现文档

## 概述

本文档描述了为dandanplay_flutter项目实现的视频播放器功能。该功能使用video_player插件实现，支持通过路由参数播放网络视频和本地视频文件。

## 实现的功能

### 1. 核心功能
- ✅ 支持网络视频URL播放（HTTP/HTTPS）
- ✅ 支持本地视频文件播放
- ✅ 播放控制（播放/暂停、进度条）
- ✅ 全屏播放支持
- ✅ 自动播放
- ✅ 错误处理和重试机制
- ✅ 响应式UI设计

### 2. 技术特性
- ✅ 使用video_player插件
- ✅ 使用forui组件库保持UI一致性
- ✅ 支持路由参数传递
- ✅ 自动检测视频格式
- ✅ 全屏模式下的系统UI控制

## 文件结构

### 新增文件
```
lib/
├── page/
│   ├── player.dart                 # 视频播放器页面
│   └── video_demo.dart             # 演示页面
└── utils/
    └── video_player_utils.dart     # 视频播放器工具类
```

### 修改的文件
```
lib/
├── router.dart                     # 添加视频播放器路由
└── page/
    └── file_explorer.dart          # 集成视频播放功能
```

## 核心组件

### VideoPlayerPage
主要的视频播放器页面，支持：
- 网络视频和本地文件播放
- 播放控制界面
- 全屏模式
- 错误处理

### VideoPlayerUtils
工具类，提供便捷的导航和检测方法：
- `navigateToPlayer()` - 导航到播放器
- `playNetworkVideo()` - 播放网络视频
- `playLocalVideo()` - 播放本地视频
- `isSupportedVideoFormat()` - 检测支持的格式
- `isNetworkVideo()` - 检测网络视频

## 使用方法

### 1. 通过路由导航

```dart
// 使用go_router导航
context.push('/video-player?path=https://example.com/video.mp4&title=示例视频');

// 或者使用工具类
VideoPlayerUtils.navigateToPlayer(
  context,
  videoPath: 'https://example.com/video.mp4',
  title: '示例视频',
);
```

### 2. 播放网络视频

```dart
VideoPlayerUtils.playNetworkVideo(
  context,
  url: 'https://example.com/video.mp4',
  title: '网络视频标题',
);
```

### 3. 播放本地视频

```dart
VideoPlayerUtils.playLocalVideo(
  context,
  filePath: '/storage/emulated/0/Movies/video.mp4',
  title: '本地视频标题',
);
```

### 4. 在文件浏览器中集成

文件浏览器已经集成了视频播放功能，点击视频文件即可播放。

## 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MKV (.mkv)
- MOV (.mov)
- WMV (.wmv)
- FLV (.flv)
- WebM (.webm)
- M4V (.m4v)
- 3GP (.3gp)
- TS (.ts)
- M3U8 (.m3u8)

## 路由配置

### 视频播放器路由
```
路径: /video-player
参数:
  - path (必需): 视频文件路径或URL
  - title (可选): 视频标题
```

### 演示页面路由
```
路径: /video-demo
用于测试和演示视频播放功能
```

## UI特性

### 播放控制界面
- 点击屏幕显示/隐藏控制栏
- 播放/暂停按钮
- 进度条拖拽
- 全屏切换按钮
- 时间显示

### 全屏模式
- 自动横屏
- 隐藏系统UI
- 顶部显示标题和返回按钮

### 错误处理
- 加载失败时显示错误信息
- 提供重试按钮
- 友好的错误提示

## 演示页面

访问 `/video-demo` 路由可以看到演示页面，包含：
- 网络视频播放测试
- 本地视频播放测试
- 示例视频列表

## 集成示例

### 在现有页面中添加视频播放

```dart
import 'package:dandanplay_flutter/utils/video_player_utils.dart';

class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return FButton(
      onPress: () {
        VideoPlayerUtils.playNetworkVideo(
          context,
          url: 'https://example.com/video.mp4',
          title: '我的视频',
        );
      },
      child: const Text('播放视频'),
    );
  }
}
```

## 依赖项

现有依赖：
- `video_player: ^2.10.0` - 视频播放核心
- `forui` - UI组件库
- `go_router` - 路由管理

## 注意事项

### 网络视频
- 需要网络权限
- 建议使用HTTPS URL
- 某些视频格式可能需要特定的编解码器

### 本地视频
- 需要存储权限
- 确保文件路径正确
- 支持的格式取决于设备

### 性能优化
- 视频播放器会在页面销毁时自动释放资源
- 全屏模式会自动恢复系统UI设置
- 支持后台播放控制

## 故障排除

### 常见问题

1. **视频无法播放**
   - 检查网络连接
   - 验证视频URL或文件路径
   - 确认视频格式支持

2. **全屏模式问题**
   - 确保设备支持横屏
   - 检查系统UI权限

3. **性能问题**
   - 避免同时播放多个视频
   - 及时释放不用的播放器资源

## 未来增强

### 计划功能
- [ ] 音量控制
- [ ] 播放速度调节
- [ ] 字幕支持
- [ ] 播放列表
- [ ] 画中画模式
- [ ] 投屏功能

### 技术改进
- [ ] 缓存机制
- [ ] 预加载优化
- [ ] 更好的错误恢复
- [ ] 播放统计

## 总结

视频播放器功能已成功集成到dandanplay_flutter项目中，提供了完整的视频播放体验。通过工具类和路由配置，可以轻松在应用的任何地方添加视频播放功能。
