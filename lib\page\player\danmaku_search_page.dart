import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';

class DanmakuSearchPage extends StatefulWidget {
  final void Function(String episodeId) onEpisodeSelected;
  final Future<List<Anime>> Function(String name) searchEpisodes;

  const DanmakuSearchPage({
    super.key,
    required this.onEpisodeSelected,
    required this.searchEpisodes,
  });

  @override
  State<DanmakuSearchPage> createState() => _DanmakuSearchPageState();
}

class _DanmakuSearchPageState extends State<DanmakuSearchPage> {
  final _searchController = TextEditingController();
  final _globalPlayerService = GetIt.I.get<GlobalPlayerService>();
  bool _isLoading = false;
  String? _errorMessage;
  List<Anime> _animes = [];

  @override
  void initState() {
    _searchController.text = _globalPlayerService.getVideoName();
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _search() async {
    final keyword = _searchController.text.trim();
    if (keyword.isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final animes = await widget.searchEpisodes(keyword);
      setState(() {
        _animes = animes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: FAccordion(
          controller: FAccordionController(),
          children: [
            _buildSearchBar(context),
            const SizedBox(height: 8),
            ..._buildBody(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: FTextField(controller: _searchController, hint: '输入动画或剧集名称'),
        ),
        const SizedBox(width: 8),
        FButton.icon(
          onPress: _isLoading ? () {} : _search,
          child:
              _isLoading
                  // 固定大小
                  ? const SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      padding: EdgeInsets.all(2),
                    ),
                  )
                  : Icon(
                    Icons.search,
                    size: 30,
                    color: context.theme.colors.primary,
                  ),
        ),
      ],
    );
  }

  List<Widget> _buildBody() {
    if (_errorMessage != null) {
      return [
        Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('错误: $_errorMessage', textAlign: TextAlign.center),
          ),
        ),
      ];
    }

    return _animes.map((anime) {
      return FAccordionItem(
        title: Text(anime.animeTitle),
        // 添加分割线
        child: ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: anime.episodes.length * 2 - 1,
          itemBuilder: (context, index) {
            if (index % 2 == 1) {
              return Divider(height: 1, color: context.theme.colors.border);
            }
            final episode = anime.episodes[(index / 2).round()];
            return ListTile(
              title: Text(episode.episodeTitle),
              onTap: () {
                widget.onEpisodeSelected(episode.episodeId.toString());
              },
            );
          },
        ),
      );
    }).toList();
  }
}
