import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:signals/signals.dart';

class GlobalPlayerService {
  String currentVideoPath = '';
  Map<String, String> headers = {};
  Signal<int> currentIndex = Signal(0);
  late BuildContext notificationContext;

  static Future<void> register() async {
    final service = GlobalPlayerService();
    GetIt.I.registerSingleton<GlobalPlayerService>(service);
  }

  void showNotification(String message) {
    showRawFToast(
      context: notificationContext,
      alignment: FToastAlignment.bottomLeft,
      duration: Duration(seconds: 3),
      builder: (context, entry) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(message, style: TextStyle(color: Colors.white)),
        );
      },
    );
  }

  String getVideoName() {
    return currentVideoPath.split('/').last.split('.').first;
  }
}
